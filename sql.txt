create table r_files
(
    id                varchar(100) not null comment '主键id',
    parent_id         varchar(100) null,
    report_name       varchar(200) null comment '报表名称',
    file_name         varchar(500) null comment '文件名称',
    create_date       datetime     null comment '创建时间',
    update_date       datetime     null comment '修改时间',
    content           blob         null comment 'xml的内容',
    publish_menu_name varchar(100) null,
    sheet_names       varchar(255) null comment 'sheet名称',
    menu_id           varchar(100) null,
    update_by         varchar(100) null
)
    charset = utf8;


create table r_file_type_mapping
(
    report_id varchar(100) null comment '报表id',
    group_id  varchar(100) null comment '类型id',
    id        varchar(100) null comment '主键id'
)
    comment '报表文件和类型映射表' charset = utf8;


create table r_conditions
(
    cell_style varchar(4000) null,
    name       varchar(100)  null,
    conditions varchar(4000) null,
    id         varchar(100)  null
)
    charset = utf8;

create table r_group_info
(
    id         varchar(100) null,
    group_name varchar(100) null
)
    charset = utf8;
