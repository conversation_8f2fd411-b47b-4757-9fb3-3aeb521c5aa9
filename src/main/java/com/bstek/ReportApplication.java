package com.bstek;

import com.bstek.common.utils.PropertiesUtils;
import org.apache.commons.lang3.StringUtils;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.scheduling.annotation.EnableAsync;

@SpringBootApplication
@EnableAsync
@MapperScan(basePackages = "com.bstek.*.mapper")
public class ReportApplication {

	public static void main(String[] args) {

		SpringApplication.run(ReportApplication.class, args);
	}
}
