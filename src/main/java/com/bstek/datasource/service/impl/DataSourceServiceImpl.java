package com.bstek.datasource.service.impl;

import java.beans.PropertyDescriptor;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.*;

import javax.annotation.Resource;
import javax.sql.DataSource;

import com.bstek.common.utils.impala.ImpalaUtils;
import com.bstek.designer.Utils.PlatFormUtils;
import com.bstek.system.mapper.OperationLogMapper;
import com.bstek.ureport.definition.dataset.Parameter;
import org.apache.commons.beanutils.PropertyUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.ResultSetExtractor;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.jdbc.datasource.SingleConnectionDataSource;
import org.springframework.jdbc.support.JdbcUtils;
import org.springframework.stereotype.Service;

import com.bstek.common.config.DataSourceConfig;
import com.bstek.common.exception.ReportDesignException;
import com.bstek.common.utils.MultipleJdbcTemplate;
import com.bstek.datasource.bean.DataSourceInfo;
import com.bstek.datasource.bean.PreviewParams;
import com.bstek.datasource.service.DataSourceService;
import com.bstek.ureport.definition.dataset.Field;
import com.bstek.ureport.definition.dataset.SqlDatasetDefinition;
import com.bstek.ureport.utils.ProcedureUtils;

@Service
public class DataSourceServiceImpl implements DataSourceService {

	@Resource
	private DataSourceConfig dataSourceConfig;

	@Resource
	private OperationLogMapper operationLogMapper;

	@Override
	public List<Map<String, String>> selectBuildinDruidList() {
		List<Map<String,String>> druids = new ArrayList<Map<String,String>>();
		List<DataSourceInfo> list = dataSourceConfig.getDatasource();
		if(list != null && list.size() > 0) {
			for (DataSourceInfo info : list) {
				Map<String,String> map = new HashMap<String, String>();
				try {
					map.put("name", info.getName());
					map.put("code", info.getCode());
				} catch (Exception e) {
					e.printStackTrace();
				}
				druids.add(map);
			}
		}
		return druids;
	}

	@Override
	public List<Map<String, String>> selectTableList(DataSourceInfo info) {
		List<Map<String,String>> tables = new ArrayList<Map<String,String>>();
		Connection conn = null;
		ResultSet rs = null;
		try {
			String username = info.getUserName();
			String password = info.getPassword();
			String driver = info.getDriverClassName();
			String url = info.getUrl();
			// 内置jdbc查询
			if(StringUtils.isBlank(driver) || StringUtils.isBlank(url) || StringUtils.isBlank(username) || StringUtils.isBlank(password)) {
				info = DataSourceConfig.getDataSourceInfo(info.getName());
			}
			if (info.getName() != null && info.getName().startsWith("impala")) {
				conn = ImpalaUtils.getConn(info.getUrl(),info.getUserName(),info.getPassword());
			}else {
				conn = MultipleJdbcTemplate.buildConnection(info);
			}
			DatabaseMetaData metaData = conn.getMetaData();
			String dbName = conn.getCatalog();
			String schema = info.getUrl().toLowerCase().contains("oracle") ? metaData.getUserName(): conn.getSchema();
			if (info.getName().startsWith("impala")) {
				rs = metaData.getTables(null, null, null, new String[] { "TABLE" });
			}else {
				rs = metaData.getTables(dbName, schema, "%", new String[] { "TABLE","VIEW" });
			}

			while (rs.next()) {
				Map<String,String> table = new HashMap<String, String>();
				table.put("name",rs.getString("TABLE_NAME"));
				table.put("type",rs.getString("TABLE_TYPE"));
				tables.add(table);
			}

		} catch (Exception ex) {
			throw new RuntimeException(ex);
		} finally {
			JdbcUtils.closeResultSet(rs);
			JdbcUtils.closeConnection(conn);
		}
		return tables;
	}

	@Override
	public List<Field> selectFieldList(PreviewParams previewParams) {
		DataSourceInfo info = previewParams.getInfo();
		String username = info.getUserName();
		String password = info.getPassword();
		String driver = info.getDriverClassName();
		String url = info.getUrl();
		// 内置jdbc查询
		if(StringUtils.isBlank(driver) || StringUtils.isBlank(url) || StringUtils.isBlank(username) || StringUtils.isBlank(password)) {
			info = DataSourceConfig.getDataSourceInfo(info.getName());
		}
		Connection conn = null;
		try {
			if (info.getName().startsWith("impala")) {
				conn = ImpalaUtils.getConn(info.getUrl(),info.getUserName(),info.getPassword());
			}else {
				conn = MultipleJdbcTemplate.buildConnection(info);
			}
			//conn = MultipleJdbcTemplate.buildConnection(info);

			SqlDatasetDefinition sqlDatasetDefinition = new SqlDatasetDefinition();
			sqlDatasetDefinition.setSql(previewParams.getSql());
			sqlDatasetDefinition.setParameters(previewParams.getParameters());

			Map<String, Object> params = new HashMap<String, Object>();
			Map<String, Object> pmap = sqlDatasetDefinition.buildParameters(params);
			for (Map.Entry<String, Object> entry : pmap.entrySet()){
				if ("T-1".equals(entry.getValue())){
					pmap.put(entry.getKey(), operationLogMapper.getBusiDate(LocalDateTime.now().toString()));
				}
				if ("T-2".equals(entry.getValue())){
					pmap.put(entry.getKey(), operationLogMapper.getYesterDate(LocalDateTime.now().toString()));
				}
				if ("当天".equals(entry.getValue())){
					pmap.put(entry.getKey(), com.bstek.common.utils.StringUtils.getTodayDateFormatter(LocalDateTime.now()));
				}
			}
			String sqlForUse = sqlDatasetDefinition.parseSql(pmap);
			if (ProcedureUtils.isProcedure(sqlForUse)) {
				return ProcedureUtils.procedureColumnsQuery(sqlForUse, pmap, conn);
			}
			DataSource dataSource = new SingleConnectionDataSource(conn, false);
			NamedParameterJdbcTemplate jdbcTemplate = new NamedParameterJdbcTemplate(dataSource);
			final List<Field> fields = new ArrayList<Field>();
			jdbcTemplate.query(sqlForUse, pmap, new ResultSetExtractor<Integer>() {
				@Override
				public Integer extractData(ResultSet rs) throws SQLException, DataAccessException {
					ResultSetMetaData metadata = (ResultSetMetaData) rs.getMetaData();
					List<String> metadataList = new ArrayList<String>();
					for (int i = 1; i <= metadata.getColumnCount(); i++) {
						String columnName = metadata.getColumnLabel(i);
						metadataList.add(columnName);
						fields.add(new Field(columnName));
					}
					return null;
				}
			});
			return fields;

		} catch (Exception ex) {
			throw new RuntimeException(ex);
		} finally {
			JdbcUtils.closeConnection(conn);
		}
	}

	@Override
	public Map<String, Object> previewData(PreviewParams previewParams) {
		Map<String, Object> data = new HashMap<>();

		DataSourceInfo info = previewParams.getInfo();
		String busiDate = operationLogMapper.getBusiDate(com.bstek.common.utils.StringUtils.getTodayDateFormatter(LocalDateTime.now()));
		String yesterday  = operationLogMapper.getYesterDate(com.bstek.common.utils.StringUtils.getTodayDateFormatter(LocalDateTime.now()));

		for (Parameter parameter : previewParams.getParameters()) {
			if ("T-1".equals(parameter.getDefaultValue())) {
				parameter.setDefaultValue(busiDate);
			}
			if ("T-2".equals(parameter.getDefaultValue())) {
				parameter.setDefaultValue(yesterday);
			}
		}

		String username = info.getUserName();
		String password = info.getPassword();
		String driver = info.getDriverClassName();
		String url = info.getUrl();

		if (StringUtils.isBlank(driver) || StringUtils.isBlank(url) || StringUtils.isBlank(username) || StringUtils.isBlank(password)) {
			info = DataSourceConfig.getDataSourceInfo(info.getName());
		}

		Connection conn = null;
		ExecutorService executor = Executors.newSingleThreadExecutor();
		Future<Map<String, Object>> future = null;

		try {
			if (info.getName().startsWith("impala")) {
				conn = ImpalaUtils.getConn(info.getUrl(), info.getUserName(), info.getPassword());
			} else {
				conn = MultipleJdbcTemplate.buildConnection(info);
			}

			SqlDatasetDefinition sqlDatasetDefinition = new SqlDatasetDefinition();
			sqlDatasetDefinition.setSql(previewParams.getSql());
			sqlDatasetDefinition.setParameters(previewParams.getParameters());

			Map<String, Object> params = new HashMap<>();
			Map<String, Object> pmap = sqlDatasetDefinition.buildParameters(params);

			for (Map.Entry<String, Object> entry : pmap.entrySet()) {
				if ("T-1".equals(entry.getValue())) {
					pmap.put(entry.getKey(), operationLogMapper.getBusiDate(LocalDateTime.now().toString()));
				}
				if ("T-2".equals(entry.getValue())) {
					pmap.put(entry.getKey(), operationLogMapper.getYesterDate(LocalDateTime.now().toString()));
				}
			}

			String sqlForUse = sqlDatasetDefinition.parseSql(pmap);
			data.put("sql", sqlForUse);

			int row = 100;

			if (ProcedureUtils.isProcedure(sqlForUse)) {
				return ProcedureUtils.procedureQuery(sqlForUse, pmap, conn, row);
			} else {
				List<String> fieldList = new LinkedList<>();
				data.put("fields", fieldList);

				DataSource dataSource = new SingleConnectionDataSource(conn, false);
				NamedParameterJdbcTemplate jdbcTemplate = new NamedParameterJdbcTemplate(dataSource);

				Callable<Map<String, Object>> task = () -> {
					List<Map<String, Object>> list = jdbcTemplate.query(sqlForUse, pmap, rs -> {
						List<Map<String, Object>> resultList = new LinkedList<>();
						ResultSetMetaData metadata = rs.getMetaData();
						int count = metadata.getColumnCount();
						for (int i = 1; i <= count; i++) {
							String columnName = metadata.getColumnLabel(i);
							fieldList.add(columnName);
						}

						int index = 0;
						while (rs.next() && index < row) {
							int initialCapacity = (int) ((float) fieldList.size() / 0.75F + 1.0F);
							HashMap<String, Object> map = new HashMap<>(initialCapacity);
							int i = 1;
							for (String field : fieldList) {
								Object value = JdbcUtils.getResultSetValue(rs, i);
								map.put(field, value);
								i++;
							}
							index++;
							resultList.add(map);
						}
						return resultList;
					});

					Map<String, Object> result = new HashMap<>();
					result.put("data", list);
					return result;
				};

				future = executor.submit(task);

				// 设置超时时间为3分钟
				data.putAll(future.get(2, TimeUnit.MINUTES));
			}

		} catch (InterruptedException | ExecutionException | ClassNotFoundException | IOException | SQLException e) {
			String message = e.getMessage();
//			 message = message.replaceAll("\\[.*?\\]", "");
			data.put("message", "查询执行失败: " + message);
		} catch (TimeoutException e) {
			data.put("message", "该SQL执行时间过长！");
		} finally {
			if (future != null) {
				future.cancel(true); // 取消任务
			}
			JdbcUtils.closeConnection(conn);
			executor.shutdownNow(); // 关闭线程池
		}

		return data;
	}
	@Override
	public List<Field> getSpringBeanFieldList(String clazz) {
		List<Field> result = new ArrayList<Field>();
		try {
			Class<?> targetClass = Class.forName(clazz);
			PropertyDescriptor[] propertyDescriptors = PropertyUtils.getPropertyDescriptors(targetClass);
			for(PropertyDescriptor pd:propertyDescriptors){
				String name = pd.getName();
				if("class".equals(name)){
					continue;
				}
				result.add(new Field(name));
			}
			return result;

		}catch(Exception ex){
			throw new ReportDesignException(ex);
		}
	}

	public static void main(String[] args) {
		String  input = " org,.sprinaframework.idbc,BadSalGrammarException: PreparedStatementCallback:bad SQL grammar [select * fom cig_data_revie" +
				"w WHERE suc='hdfidshikfhsdikhfiksdhfikshkithiksdhfkisdhikfhsdikhfkjdshfikhsdkithskidhfkisdhikishdkithskdithkisdhikihsdkithskidhikisdhikishdkiths" +
				"kifhkisdhfkishdikhsdkifhkisdhfiksdhikhsdkifhsdki”:] nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'suc' in 'where clause";
		//将字符串里的 [ ] 中间的内容去掉
		String result = input.replaceAll("\\[.*?\\]", "");

		System.out.println("原始字符串: " + input);
		System.out.println("处理后的字符串: " + result);
//		System.out.println(a);
	}
}
