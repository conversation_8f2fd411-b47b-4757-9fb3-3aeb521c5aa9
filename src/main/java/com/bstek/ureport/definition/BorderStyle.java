/*******************************************************************************
 * Copyright 2017 Bstek
 * 
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License.  You may obtain a copy
 * of the License at
 * 
 *   http://www.apache.org/licenses/LICENSE-2.0
 * 
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
 * License for the specific language governing permissions and limitations under
 * the License.
 ******************************************************************************/
package com.bstek.ureport.definition;
/**
*
*
 */
public enum BorderStyle {
	solid,dashed,doublesolid;
	public static BorderStyle toBorderStyle(String name){
		if(name.equals("double")){
			return BorderStyle.doublesolid;
		}else{
			return BorderStyle.valueOf(name);
		}
	}
	@Override
	public String toString() {
		if(this.equals(BorderStyle.doublesolid)){
			return "double";
		}
		return super.toString();
	}
}
