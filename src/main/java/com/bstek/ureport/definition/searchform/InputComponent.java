/*******************************************************************************
 * Copyright 2017 Bstek
 * 
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License.  You may obtain a copy
 * of the License at
 * 
 *   http://www.apache.org/licenses/LICENSE-2.0
 * 
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
 * License for the specific language governing permissions and limitations under
 * the License.
 ******************************************************************************/
package com.bstek.ureport.definition.searchform;

import com.bstek.ureport.export.html.InputComponentData;

/**
*
*
 */
public abstract class InputComponent implements Component {
	
	private String uuid;
	
	private String label;
	
	private String bindParameter;
	
	private String type;
	
	public String getLabel() {
		return label;
	}

	public void setLabel(String label) {
		this.label = label;
	}

	public String getBindParameter() {
		return bindParameter;
	}

	public void setBindParameter(String bindParameter) {
		this.bindParameter = bindParameter;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}
	
	public String getUuid() {
		return uuid;
	}

	public void setUuid(String uuid) {
		this.uuid = uuid;
	}

	public abstract InputComponentData buildComponent(RenderContext context);
	
	public void setInputComponentData(InputComponentData data) {
		data.setUuid(getUuid());
		data.setLabel(getLabel());
		data.setType(getType());
		data.setBindParameter(getBindParameter());
	}
}
