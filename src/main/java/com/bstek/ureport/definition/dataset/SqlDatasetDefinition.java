/*******************************************************************************
 * Copyright 2017 Bstek
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License.  You may obtain a copy
 * of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
 * License for the specific language governing permissions and limitations under
 * the License.
 ******************************************************************************/
package com.bstek.ureport.definition.dataset;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import com.bstek.designer.bean.PO.ReportDataPermission;
import com.bstek.system.mapper.OperationLogMapper;
import com.bstek.ureport.utils.StringUtils;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.ResultSetExtractor;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.jdbc.datasource.SingleConnectionDataSource;
import org.springframework.jdbc.support.JdbcUtils;

import com.bstek.ureport.Utils;
import com.bstek.ureport.build.Context;
import com.bstek.ureport.build.Dataset;
import com.bstek.ureport.definition.datasource.DataType;
import com.bstek.ureport.expression.ExpressionUtils;
import com.bstek.ureport.expression.model.Expression;
import com.bstek.ureport.expression.model.data.ExpressionData;
import com.bstek.ureport.expression.model.data.ObjectExpressionData;
import com.bstek.ureport.utils.ProcedureUtils;

import javax.annotation.Resource;

/**
*
 */
@Slf4j
public class SqlDatasetDefinition implements DatasetDefinition {


	private static final long serialVersionUID = -1134526105416805870L;

	private String name;

	private String sql;

	private List<Parameter> parameters;

	private List<Field> fields;

	private Expression sqlExpression;

	/**
	 * 过滤数据集中配置了部门字段的数据
	 * @param params
	 * @param resultList
	 * @return
	 */
	private List<Map<String, Object>> filterData (Map<String, Object> params, List<Map<String, Object>> resultList) {
		log.info("SqlDatasetDefinition#filterData开始过滤部门字段的数据,参数为：{}，待处理条数为{}", new Gson().toJson(params), resultList.size());
		// 1. 先从params中取出该报表对应的部门字段以及当前用户的角色部门值
		List<String> orgIds;
		List<ReportDataPermission> dataPermissionConf = Lists.newArrayList();
		String datasetCol = "";
		if (params.containsKey("orgIds") && params.containsKey("dataPermissionConf")) {
			orgIds = (List<String>) params.get("orgIds");
			dataPermissionConf = (List<ReportDataPermission>) params.get("dataPermissionConf");
			List<ReportDataPermission> confs = dataPermissionConf.stream().filter(m -> m.getDataset().equals(name)).collect(Collectors.toList());
			if (!confs.isEmpty()){
				datasetCol = confs.get(0).getDatasetCol();
			}
			//将 fields 的 File 的 name 属性抽出为 List<String>
			List<String> fieldNames = fields.stream().map(Field::getName).collect(Collectors.toList());
			log.info("当前报表有字段值{}，开始处理", fieldNames);
			if (!fieldNames.contains(datasetCol)) {
				log.info("当前字段值{}在数据集中无当前字段，置空不处理", datasetCol);
				datasetCol = "";
			}
			log.info("取参完毕，orgIds条数：{}, 配置的字段为：{}", orgIds.size() ,datasetCol);
		} else {
			orgIds = Lists.newArrayList();
		}

		if (orgIds.size() == 0 && StringUtils.isNotBlank(datasetCol)) {
			resultList = Lists.newArrayList();
		}else if (StringUtils.isBlank(datasetCol)) {
			log.info("当前字段{}为空，不需过滤", datasetCol);
		}else {
			String finalDatasetCol = datasetCol;
			resultList = resultList.stream().filter(m -> orgIds.contains(m.get(finalDatasetCol))).collect(Collectors.toList());

		}
		log.info("SqlDatasetDefinition#filterData过滤部门字段的数据结束,过滤完毕后条数为:{}", resultList.size());
		return resultList;
	}

	public Dataset buildDataset(Map<String, Object> params, Connection conn) {
		long start = System.currentTimeMillis();
		Map<String, Object> pmap = buildParameters(params);
		String sqlForUse = parseSql(pmap);

		if (ProcedureUtils.isProcedure(sqlForUse)) {
			List<Map<String, Object>> result = ProcedureUtils.procedureQuery(sqlForUse, pmap, conn);
			return new Dataset(name, result);
		}

		log.info("SQL参数:" + pmap);

		ExecutorService executor = Executors.newSingleThreadExecutor();
		Future<Dataset> future = null;

		try {
			Callable<Dataset> task = () -> {
				SingleConnectionDataSource datasource = new SingleConnectionDataSource(conn, false);
				NamedParameterJdbcTemplate jdbcTemplate = new NamedParameterJdbcTemplate(datasource);
				List<Map<String, Object>> list = jdbcTemplate.query(sqlForUse, pmap, rs -> {
					List<Map<String, Object>> resultList = new LinkedList<>();
					rs.setFetchSize(512);
					while (rs.next()) {
						HashMap<String, Object> map = new HashMap<>();
						int i = 1;
						for (Field field : fields) {
							Object value = JdbcUtils.getResultSetValue(rs, i);
							map.put(field.getName(), value);
							i++;
						}
						resultList.add(map);
					}
					return resultList;
				});
				long end = System.currentTimeMillis();
				log.info("SQL查询耗时:" + (end - start) + "ms");
				// 处理数据集 过滤部门字段做权限控制
				list = filterData(params, list);
				return new Dataset(name, list);
			};

			future = executor.submit(task);

			// 设置超时时间为3分钟
			return future.get(5, TimeUnit.MINUTES);

		} catch (InterruptedException | ExecutionException e) {
			log.error("查询执行失败: " + e.getMessage());
			return new Dataset(name, new LinkedList<>(), "查询执行失败: " + e.getMessage());
		} catch (TimeoutException e) {
			log.error("执行时间过长");
			return new Dataset(name, new LinkedList<>(), "执行时间过长");
		} finally {
			if (future != null) {
				future.cancel(true); // 取消任务
			}
			executor.shutdownNow(); // 关闭线程池
		}
	}

	public String parseSql(Map<String, Object> params) {
		String sqlForUse = sql.trim();
		Context context = new Context(params);
		if (sqlForUse.startsWith(ExpressionUtils.EXPR_PREFIX) && sqlForUse.endsWith(ExpressionUtils.EXPR_SUFFIX)) {
			sqlForUse = sqlForUse.substring(2, sqlForUse.length() - 1);
			Expression expr = ExpressionUtils.parseExpression(sqlForUse);
			sqlForUse = executeSqlExpr(expr, context);
			return sqlForUse;
		} else if (sqlExpression != null) {
			sqlForUse = executeSqlExpr(sqlExpression, context);
		} else {
			Pattern pattern = Pattern.compile("\\$\\{.*?\\}");
			Matcher matcher = pattern.matcher(sqlForUse);
			while (matcher.find()) {
				String substr = matcher.group();
				String sqlExpr = substr.substring(2, substr.length() - 1);
				Expression expr = ExpressionUtils.parseExpression(sqlExpr);
				String result = executeSqlExpr(expr, context);
				sqlForUse = sqlForUse.replace(substr, result);
			}
		}
		log.info("SQL:" + sqlForUse);
		return sqlForUse;
	}

	private String executeSqlExpr(Expression sqlExpr, Context context) {
		String sqlForUse = null;
		ExpressionData<?> exprData = sqlExpr.execute(null, null, context);
		if (exprData instanceof ObjectExpressionData) {
			ObjectExpressionData data = (ObjectExpressionData) exprData;
			Object obj = data.getData();
			if (obj != null) {
				String s = obj.toString();
				s = s.replaceAll("\\\\", "");
				sqlForUse = s;
			}
		}
		return sqlForUse;
	}

	public Map<String, Object> buildParameters(Map<String, Object> params) {
		Map<String, Object> map = new HashMap<String, Object>();
		for (Parameter param : parameters) {
			String name = param.getName();
			DataType datatype = param.getType();
			Object value = param.getDefaultValue();
//			if ("T-1".equals(value)){
//				param.setDefaultValue("");
//			}
//			if ("T-2".equals(value)){
//				param.setDefaultValue("");
//			}
			if (params != null && params.containsKey(name)) {
				value = params.get(name);
			}
			map.put(name, datatype.parse(value));
		}
		return map;
	}

	@Override
	public List<Field> getFields() {
		return fields;
	}

	public void setSqlExpression(Expression sqlExpression) {
		this.sqlExpression = sqlExpression;
	}

	public void setFields(List<Field> fields) {
		this.fields = fields;
	}

	public List<Parameter> getParameters() {
		return parameters;
	}

	public void setParameters(List<Parameter> parameters) {
		this.parameters = parameters;
	}

	@Override
	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public void setSql(String sql) {
		this.sql = sql;
	}

	public String getSql() {
		return sql;
	}

	public boolean hasParameter(String key) {
		if(parameters != null &&  parameters.size() > 0) {
			for (Parameter p : parameters) {
				if(p.getName().equals(key)) {
					return true;
				}
			}
		}
		return false;
	}
}
