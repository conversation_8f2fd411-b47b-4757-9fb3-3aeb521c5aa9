/*******************************************************************************
 * Copyright 2017 Bstek
 * 
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License.  You may obtain a copy
 * of the License at
 * 
 *   http://www.apache.org/licenses/LICENSE-2.0
 * 
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
 * License for the specific language governing permissions and limitations under
 * the License.
 ******************************************************************************/
package com.bstek.ureport.build;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import com.bstek.common.utils.StringUtils;
import com.bstek.designer.bean.PO.ReportDataPermission;
import com.bstek.designer.mapper.ReportDataPermissionMapper;
import com.bstek.designer.service.ReportDataPermissionService;
import com.bstek.system.action.OperationLogAction;
import com.bstek.system.mapper.OperationLogMapper;
import com.bstek.ureport.Utils;
import com.bstek.ureport.build.cell.CellBuilder;
import com.bstek.ureport.build.cell.NoneExpandBuilder;
import com.bstek.ureport.build.cell.down.DownExpandBuilder;
import com.bstek.ureport.build.cell.right.RightExpandBuilder;
import com.bstek.ureport.build.paging.BasePagination;
import com.bstek.ureport.build.paging.Page;
import com.bstek.ureport.build.paging.PagingBuilder;
import com.bstek.ureport.definition.Band;
import com.bstek.ureport.definition.Expand;
import com.bstek.ureport.definition.Orientation;
import com.bstek.ureport.definition.PagingMode;
import com.bstek.ureport.definition.Paper;
import com.bstek.ureport.definition.ReportDefinition;
import com.bstek.ureport.definition.dataset.DatasetDefinition;
import com.bstek.ureport.definition.dataset.SqlDatasetDefinition;
import com.bstek.ureport.definition.datasource.ApiDatasourceDefinition;
import com.bstek.ureport.definition.datasource.BuildinDatasourceDefinition;
import com.bstek.ureport.definition.datasource.DatasourceDefinition;
import com.bstek.ureport.definition.datasource.JdbcDatasourceDefinition;
import com.bstek.ureport.definition.datasource.SpringBeanDatasourceDefinition;
import com.bstek.ureport.exception.ReportException;
import com.bstek.ureport.model.Cell;
import com.bstek.ureport.model.Column;
import com.bstek.ureport.model.Report;
import com.bstek.ureport.model.Row;
import lombok.extern.slf4j.Slf4j;
import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
*
 */
@Slf4j
@Component
public class ReportBuilder extends BasePagination {

	public static final String BEAN_ID = "ureport.reportBuilder";

	private Map<Expand, CellBuilder> cellBuildersMap = new HashMap<Expand, CellBuilder>();

	private NoneExpandBuilder noneExpandBuilder = new NoneExpandBuilder();

	private HideRowColumnBuilder hideRowColumnBuilder = new HideRowColumnBuilder();

	private static final ExecutorService executor = Executors.newFixedThreadPool(10); // 根据需要调整线程池大小

	@Autowired
	private OperationLogAction operatorLogAction ;

	@Autowired
	private OperationLogMapper operationLogMapper;




	public ReportBuilder() {
		cellBuildersMap.put(Expand.Right, new RightExpandBuilder());
		cellBuildersMap.put(Expand.Down, new DownExpandBuilder());
		cellBuildersMap.put(Expand.None, noneExpandBuilder);
	}

	public Report buildReport(ReportDefinition reportDefinition, Map<String, Object> parameters) {
		Report report = reportDefinition.newReport();
		Map<String, Dataset> datasetMap = buildDatasets(reportDefinition, parameters);
		//循环取 datasetMap 的数据 Dataset 中msg的值
		// 提取第一个有值的msg
		String msg = datasetMap.values().stream()
				.map(Dataset::getMsg)
				.filter(Objects::nonNull)
				.findFirst()
				.orElse(null);
		report.setMsg(msg);
		Context context = new Context(this, report, datasetMap, parameters, hideRowColumnBuilder);
		long start = System.currentTimeMillis();
		List<Cell> cells = new ArrayList<Cell>();
		cells.add(report.getRootCell());
		do {
			buildCell(context, cells);
			cells = context.nextUnprocessedCells();
		} while (cells != null);
		doFillBlankRows(report, context);
		recomputeCells(report, context);
		long end = System.currentTimeMillis();
		log.info("报表计算耗时:" + (end - start) + "ms");
		return report;
	}

	public void buildCell(Context context, List<Cell> cells) {
		if (cells == null) {
			cells = context.nextUnprocessedCells();
		}
		if (cells == null) {
			return;
		}
		for (Cell cell : cells) {
			List<BindData> dataList = context.buildCellData(cell);
			cell.setProcessed(true);
			int size = dataList.size();
			Cell lastCell = cell;
			if (size == 1) {
				lastCell = noneExpandBuilder.buildCell(dataList, cell, context);
			} else if (size > 1) {
				CellBuilder cellBuilder = cellBuildersMap.get(cell.getExpand());
				lastCell = cellBuilder.buildCell(dataList, cell, context);
			}
			if (lastCell.isFillBlankRows() && lastCell.getMultiple() > 0) {
				int result = size % lastCell.getMultiple();
				if (result > 0) {
					int value = lastCell.getMultiple() - result;
					context.addFillBlankRow(lastCell.getRow(), value);
				}
			}
		}
	}

	/**
	 * 根据报告定义和参数构建数据集映射
	 *
	 * 本方法遍历报告定义中的数据源列表，并根据不同的数据源类型实例化相应的数据源对象，
	 * 通过调用数据源对象的buildDatasets方法获取数据集列表，然后将这些数据集添加到一个映射中，
	 * 以便后续在生成报告时使用适当的数据集数据该方法处理的数据源类型包括JDBC数据源、内置数据源、
	 * API数据源和Spring Bean数据源
	 *
	 * @param reportDefinition 报告定义对象，包含了报告所需的数据源信息
	 * @param parameters 报告生成过程中的一组参数，可能影响数据集的构建
	 * @return 返回一个映射，其中键是数据集的名称，值是数据集对象本身
	 */
	public Map<String, Dataset> buildDatasets(ReportDefinition reportDefinition, Map<String, Object> parameters) {
		Map<String, Dataset> datasetMap = new HashMap<String, Dataset>();
		List<DatasourceDefinition> datasources = reportDefinition.getDatasources();
		if (datasources == null) {
			return datasetMap;
		}
		long start = System.currentTimeMillis();
		for (DatasourceDefinition dsDef : datasources) {
			if (dsDef instanceof JdbcDatasourceDefinition) {
				JdbcDatasourceDefinition ds = (JdbcDatasourceDefinition) dsDef;
				List<Dataset> ls = ds.buildDatasets(parameters);
				if (ls != null) {
					for (Dataset dataset : ls) {
						datasetMap.put(dataset.getName(), dataset);
					}
				}
			} else if (dsDef instanceof BuildinDatasourceDefinition) {
				BuildinDatasourceDefinition ds = (BuildinDatasourceDefinition) dsDef;
				List<Dataset> ls = ds.buildDatasets(parameters);
				if (ls != null) {
					for (Dataset dataset : ls) {
						datasetMap.put(dataset.getName(), dataset);
					}
				}
			} else if (dsDef instanceof ApiDatasourceDefinition) {
				ApiDatasourceDefinition ds = (ApiDatasourceDefinition) dsDef;
				List<Dataset> ls = ds.buildDatasets(parameters);
				if (ls != null) {
					for (Dataset dataset : ls) {
						datasetMap.put(dataset.getName(), dataset);
					}
				}
			} else if (dsDef instanceof SpringBeanDatasourceDefinition) {
				SpringBeanDatasourceDefinition ds = (SpringBeanDatasourceDefinition) dsDef;
				List<Dataset> ls = ds.buildDatasets(parameters);
				if (ls != null) {
					for (Dataset dataset : ls) {
						datasetMap.put(dataset.getName(), dataset);
					}
				}
			}
		}
		long end = System.currentTimeMillis();
		log.info("ReportBuilder#buildDatasets数据集构建完毕，耗时：{}", end-start + "ms");
		return datasetMap;
	}

	private void doFillBlankRows(Report report, Context context) {
		Map<Row, Integer> map = context.getFillBlankRowsMap();
		List<Row> newRowList = new ArrayList<Row>();
		for (Row row : map.keySet()) {
			int size = map.get(row);
			Row lastRow = findLastRow(row, report);
			for (int i = 0; i < size; i++) {
				Row newRow = buildNewRow(lastRow, report);
				newRowList.add(newRow);
			}
			int rowNumber = lastRow.getRowNumber();
			if (newRowList.size() > 0) {
				report.insertRows(rowNumber + 1, newRowList);
				newRowList.clear();
			}
		}
	}

	private Row buildNewRow(Row row, Report report) {
		Row newRow = row.newRow();
		newRow.setBand(null);
		List<Row> rows = report.getRows();
		List<Column> columns = report.getColumns();
		int start = -1, colSize = columns.size();
		Map<Row, Map<Column, Cell>> rowMap = report.getRowColCellMap();
		Map<Column, Cell> newCellMap = new HashMap<Column, Cell>();
		rowMap.put(newRow, newCellMap);

		Map<Column, Cell> colMap = rowMap.get(row);
		for (int index = 0; index < colSize; index++) {
			Column column = columns.get(index);
			Cell currentCell = colMap.get(column);
			if (currentCell == null) {
				if (start == -1) {
					start = row.getRowNumber() - 2;
				}
				for (int i = start; i > -1; i--) {
					Row currentRow = rows.get(i);
					Map<Column, Cell> prevColMap = rowMap.get(currentRow);
					if (prevColMap == null) {
						continue;
					}
					if (prevColMap.containsKey(column)) {
						currentCell = prevColMap.get(column);
						break;
					}
				}
			}
			if (currentCell == null) {
				throw new ReportException("Insert blank rows fail.");
			}
			int colSpan = currentCell.getColSpan();
			if (colSpan > 0) {
				colSpan--;
				index += colSpan;
			}
			int rowSpan = currentCell.getRowSpan();
			if (rowSpan > 1) {
				currentCell.setRowSpan(rowSpan + 1);
			} else {
				Cell newCell = newBlankCell(currentCell, column, report);
				newCell.setRow(newRow);
				newRow.getCells().add(newCell);
				newCellMap.put(newCell.getColumn(), newCell);
			}
		}
		return newRow;
	}

	private Row findLastRow(Row row, Report report) {
		List<Row> rows = report.getRows();
		List<Cell> cells = row.getCells();
		Row lastRow = row;
		int span = 0;
		for (Cell cell : cells) {
			int rowSpan = cell.getRowSpan();
			if (rowSpan < 2) {
				continue;
			}
			if (span == 0) {
				span = rowSpan;
			} else if (rowSpan > span) {
				span = rowSpan;
			}
		}
		if (span > 1) {
			int rowIndex = row.getRowNumber() - 1 + span - 1;
			lastRow = rows.get(rowIndex);
		}
		return lastRow;
	}

	private Cell newBlankCell(Cell cell, Column column, Report report) {
		Cell newCell = new Cell();
		newCell.setData("");
		newCell.setColSpan(cell.getColSpan());
		newCell.setConditionPropertyItems(cell.getConditionPropertyItems());
		report.addLazyCell(newCell);
		newCell.setCellStyle(cell.getCellStyle());
		newCell.setName(cell.getName());
		newCell.setColumn(column);
		column.getCells().add(newCell);
		Cell leftParent = cell.getLeftParentCell();
		if (leftParent != null) {
			newCell.setLeftParentCell(leftParent);
			leftParent.addRowChild(newCell);
		}
		Cell topParent = cell.getTopParentCell();
		if (topParent != null) {
			newCell.setTopParentCell(topParent);
			topParent.addColumnChild(newCell);
		}
		return newCell;
	}

	private void recomputeCells(Report report, Context context) {
		List<Cell> lazyCells = report.getLazyComputeCells();
		for (Cell cell : lazyCells) {
			cell.doCompute(context);
		}
		context.setDoPaging(true);
		List<Row> rows = report.getRows();
		// 最后一行为补充扩展行
		if(rows.size() > 0) {
			rows.remove(rows.size() - 1);
		}
		int rowSize = rows.size();
		Paper paper = report.getPaper();
		PagingMode pagingMode = paper.getPagingMode();
		List<Row> headerRows = report.getHeaderRepeatRows();
		List<Row> footerRows = report.getFooterRepeatRows();
		List<Row> titleRows = report.getTitleRows();
		List<Row> summaryRows = report.getSummaryRows();
		List<Page> pages = new ArrayList<Page>();
		List<Row> pageRows = new ArrayList<Row>();
		int pageIndex = 1;
		List<Row> pageRepeatHeaders = new ArrayList<Row>();
		List<Row> pageRepeatFooters = new ArrayList<Row>();
		pageRepeatHeaders.addAll(headerRows);
		pageRepeatFooters.addAll(footerRows);
		if (pagingMode.equals(PagingMode.fitpage)) {
			// int height = paper.getHeight() - paper.getBottomMargin() - paper.getTopMargin() - 5;
			int height = paper.getHeight() - paper.getBottomMargin() - paper.getTopMargin() - 25;
			if (paper.getOrientation().equals(Orientation.landscape)) {
				// height = paper.getWidth() - paper.getBottomMargin() - paper.getTopMargin() - 5;
				height = paper.getWidth() - paper.getBottomMargin() - paper.getTopMargin() - 25;
			}
			int repeatHeaderRowHeight = report.getRepeatHeaderRowHeight(),
					repeatFooterRowHeight = report.getRepeatFooterRowHeight();
			int titleRowHeight = report.getTitleRowsHeight();
			int rowHeight = titleRowHeight + repeatHeaderRowHeight + repeatFooterRowHeight;
			for (int i = 0; i < rowSize; i++) {
				Row row = rows.get(i);
				int rowRealHeight = row.getRealHeight();
				if (rowRealHeight == 0) {
					continue;
				}
				Band band = row.getBand();
				if (band != null) {
					String rowKey = row.getRowKey();
					int index = -1;
					if (band.equals(Band.headerrepeat)) {
						for (int j = 0; j < pageRepeatHeaders.size(); j++) {
							Row headerRow = pageRepeatHeaders.get(j);
							if (headerRow.getRowKey().equals(rowKey)) {
								index = j;
								break;
							}
						}
						pageRepeatHeaders.remove(index);
						pageRepeatHeaders.add(index, row);
					} else if (band.equals(Band.footerrepeat)) {
						for (int j = 0; j < pageRepeatFooters.size(); j++) {
							Row footerRow = pageRepeatFooters.get(j);
							if (footerRow.getRowKey().equals(rowKey)) {
								index = j;
								break;
							}
						}
						pageRepeatFooters.remove(index);
						pageRepeatFooters.add(index, row);
					}
					continue;
				}
				// rowHeight += rowRealHeight + 1;
				rowHeight += rowRealHeight;
				pageRows.add(row);
				row.setPageIndex(pageIndex);
				boolean overflow = false;
				if ((i + 1) < rows.size()) {
					Row nextRow = rows.get(i + 1);
					if ((rowHeight + nextRow.getRealHeight()) > height) {
						overflow = true;
					}
				}
				if (!overflow && row.isPageBreak()) {
					overflow = true;
				}
				if (overflow) {
					Page newPage = buildPage(pageRows, pageRepeatHeaders, pageRepeatFooters, titleRows, pageIndex,
							report);
					pageIndex++;
					pages.add(newPage);
					rowHeight = repeatHeaderRowHeight + repeatFooterRowHeight;
					pageRows = new ArrayList<Row>();
				}
			}
			if (pageRows.size() > 0) {
				Page newPage = buildPage(pageRows, pageRepeatHeaders, pageRepeatFooters, titleRows, pageIndex, report);
				pages.add(newPage);
			}
			report.getContext().setTotalPages(pages.size());
			buildPageHeaderFooter(pages, report);
		} else {
			int fixRows = paper.getFixRows() - headerRows.size() - footerRows.size();
			if (fixRows < 0) {
				fixRows = 1;
			}
			for (int i = 0; i < rowSize; i++) {
				Row row = rows.get(i);
				int height = row.getRealHeight();
				if (height == 0) {
					continue;
				}
				Band band = row.getBand();
				if (band != null) {
					String rowKey = row.getRowKey();
					int index = -1;
					if (band.equals(Band.headerrepeat)) {
						for (int j = 0; j < pageRepeatHeaders.size(); j++) {
							Row headerRow = pageRepeatHeaders.get(j);
							if (headerRow.getRowKey().equals(rowKey)) {
								index = j;
								break;
							}
						}
						pageRepeatHeaders.remove(index);
						pageRepeatHeaders.add(index, row);
					} else if (band.equals(Band.footerrepeat)) {
						for (int j = 0; j < pageRepeatFooters.size(); j++) {
							Row footerRow = pageRepeatFooters.get(j);
							if (footerRow.getRowKey().equals(rowKey)) {
								index = j;
								break;
							}
						}
						pageRepeatFooters.remove(index);
						pageRepeatFooters.add(index, row);
					}
					continue;
				}
				row.setPageIndex(pageIndex);
				pageRows.add(row);
				if (row.isPageBreak() || pageRows.size() >= fixRows) {
					Page newPage = buildPage(pageRows, pageRepeatHeaders, pageRepeatFooters, titleRows, pageIndex,
							report);
					pageIndex++;
					pages.add(newPage);
					pageRows = new ArrayList<Row>();
				}
			}
			if (pageRows.size() > 0) {
				Page newPage = buildPage(pageRows, pageRepeatHeaders, pageRepeatFooters, titleRows, pageIndex, report);
				pages.add(newPage);
			}
			report.getContext().setTotalPages(pages.size());
			buildPageHeaderFooter(pages, report);
		}
		if(summaryRows != null && summaryRows.size() > 0) {
			buildSummaryRows(summaryRows, pages);
		}
		PagingBuilder.computeExistPageFunctionCells(report);
		report.setPages(pages);
	}

}
