/*******************************************************************************
 * Copyright 2017 Bstek
 * 
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License.  You may obtain a copy
 * of the License at
 * 
 *   http://www.apache.org/licenses/LICENSE-2.0
 * 
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
 * License for the specific language governing permissions and limitations under
 * the License.
 ******************************************************************************/
package com.bstek.ureport.build.aggregate;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.bstek.ureport.Utils;
import com.bstek.ureport.build.BindData;
import com.bstek.ureport.build.Context;
import com.bstek.ureport.definition.value.Value;
import com.bstek.ureport.exception.ReportComputeException;
import com.bstek.ureport.expression.model.Condition;
import com.bstek.ureport.expression.model.expr.dataset.DatasetExpression;
import com.bstek.ureport.model.Cell;
import com.bstek.ureport.utils.DataUtils;

/**
*
*
 */
public class MaxAggregate extends Aggregate {

	private static final SimpleDateFormat DATE_TIME_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
	private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd");

	@Override
	public List<BindData> aggregate(DatasetExpression expr, Cell cell, Context context) {
		String datasetName = expr.getDatasetName();
		String property = expr.getProperty();
		Cell leftCell = DataUtils.fetchLeftCell(cell, context, datasetName);
		Cell topCell = DataUtils.fetchTopCell(cell, context, datasetName);
		List<Object> leftList = null, topList = null;
		if (leftCell != null) {
			leftList = leftCell.getBindData();
		}
		if (topCell != null) {
			topList = topCell.getBindData();
		}
		Object result = null;
		if (leftList == null && topList == null) {
			List<?> data = context.getDatasetData(datasetName);
			result = buildMax(data, property, cell, expr, context);
		} else if (leftList == null) {
			result = buildMax(topList, property, cell, expr, context);
		} else if (topList == null) {
			result = buildMax(leftList, property, cell, expr, context);
		} else {
			List<Object> list = null;
			Object data = null;
			String prop = null;
			if (leftList.size() > topList.size()) {
				list = topList;
				data = leftCell.getData();
				Value value = leftCell.getValue();
				DatasetExpression de = DataUtils.fetchDatasetExpression(value);
				if (de == null) {
					throw new ReportComputeException("Unsupported value: " + value);
				}
				prop = de.getProperty();
			} else {
				list = leftList;
				data = topCell.getData();
				Value value = topCell.getValue();
				DatasetExpression de = DataUtils.fetchDatasetExpression(value);
				if (de == null) {
					throw new ReportComputeException("Unsupported value: " + value);
				}
				prop = de.getProperty();
			}
			Condition condition = getCondition(cell);
			if (condition == null) {
				condition = expr.getCondition();
			}
			for (Object obj : list) {
				if (condition != null && !condition.filter(cell, cell, obj, context)) {
					continue;
				}
				Object o = Utils.getProperty(obj, prop);
				if ((o != null && data != null && (o == data || o.equals(data))) || (o == null && data == null)) {
					Object value = Utils.getProperty(obj, property);
					if (value == null || value.toString().equals("")) {
						continue;
					}
					result = compareValues(result, value);
				}
			}
		}
		List<BindData> list = new ArrayList<>();
		if (result != null) {
			if (result instanceof Number) {
				list.add(new BindData(((Number) result).doubleValue(), null));
			} else {
				list.add(new BindData(result, null));
			}
		} else {
			list.add(new BindData(0, null));
		}
		return list;
	}

	private Object buildMax(List<?> list, String property, Cell cell, DatasetExpression expr, Context context) {
		Object result = null;
		Condition condition = getCondition(cell);
		if (condition == null) {
			condition = expr.getCondition();
		}
		for (Object obj : list) {
			if (condition != null && !condition.filter(cell, cell, obj, context)) {
				continue;
			}
			Object value = Utils.getProperty(obj, property);
			if (value == null || value.toString().equals("")) {
				continue;
			}
			result = compareValues(result, value);
		}
		if (result == null) {
			result = 0;
		}
		return result;
	}

	private Object compareValues(Object currentMax, Object newValue) {
		if (currentMax == null) {
			return newValue;
		}

		if (newValue instanceof Date) {
			if (currentMax instanceof Date) {
				return ((Date) currentMax).before((Date) newValue) ? newValue : currentMax;
			} else {
				return newValue;
			}
		} else if (newValue instanceof String) {
			try {
				// 尝试解析为日期时间格式
				Date date = DATE_TIME_FORMAT.parse((String) newValue);
				if (currentMax instanceof Date) {
					return ((Date) currentMax).before(date) ? newValue : currentMax;
				} else {
					return newValue;
				}
			} catch (ParseException e) {
				try {
					// 尝试解析为日期格式
					Date date = DATE_FORMAT.parse((String) newValue);
					if (currentMax instanceof Date) {
						return ((Date) currentMax).before(date) ? newValue : currentMax;
					} else {
						return newValue;
					}
				} catch (ParseException ex) {
					// 如果解析失败，尝试将其作为数值处理
					try {
						BigDecimal bigData = Utils.toBigDecimal(newValue);
						if (currentMax instanceof BigDecimal) {
							return ((BigDecimal) currentMax).compareTo(bigData) < 0 ? newValue : currentMax;
						} else {
							return newValue;
						}
					} catch (com.bstek.ureport.exception.ConvertException ce) {
						// 如果数值解析失败，尝试作为字符串处理
						String str = (String) newValue;
						if (currentMax instanceof String) {
							return ((String) currentMax).compareTo(str) < 0 ? newValue : currentMax;
						} else {
							return newValue;
						}
					}
				}
			}
		} else {
			try {
				BigDecimal bigData = Utils.toBigDecimal(newValue);
				if (currentMax instanceof BigDecimal) {
					return ((BigDecimal) currentMax).compareTo(bigData) < 0 ? newValue : currentMax;
				} else {
					return newValue;
				}
			} catch (com.bstek.ureport.exception.ConvertException ce) {
				// 如果数值解析失败，尝试作为字符串处理
				String str = newValue.toString();
				if (currentMax instanceof String) {
					return ((String) currentMax).compareTo(str) < 0 ? newValue : currentMax;
				} else {
					return newValue;
				}
			}
		}
	}
}
