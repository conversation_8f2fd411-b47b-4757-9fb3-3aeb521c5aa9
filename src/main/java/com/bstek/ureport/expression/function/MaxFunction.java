/*******************************************************************************
 * Copyright 2017 Bstek
 * 
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License.  You may obtain a copy
 * of the License at
 * 
 *   http://www.apache.org/licenses/LICENSE-2.0
 * 
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
 * License for the specific language governing permissions and limitations under
 * the License.
 ******************************************************************************/
package com.bstek.ureport.expression.function;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.StringUtils;

import com.bstek.ureport.Utils;
import com.bstek.ureport.build.BindData;
import com.bstek.ureport.build.Context;
import com.bstek.ureport.expression.model.data.BindDataListExpressionData;
import com.bstek.ureport.expression.model.data.ExpressionData;
import com.bstek.ureport.expression.model.data.ObjectExpressionData;
import com.bstek.ureport.expression.model.data.ObjectListExpressionData;
import com.bstek.ureport.model.Cell;

/**
*
 */
public class MaxFunction implements Function {

	private static final SimpleDateFormat DATE_TIME_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
	private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd");

	@Override
	public Object execute(List<ExpressionData<?>> dataList, Context context, Cell currentCell) {
		if (dataList == null || dataList.isEmpty()) {
			return null;
		}

		BigDecimal maxValue = null;
		Date maxDate = null;
		String maxDateString = null; // 用于保存最大日期的原始字符串格式
		String maxString = null; // 用于保存最大字符串

		for (ExpressionData<?> exprData : dataList) {
			if (exprData instanceof ObjectListExpressionData) {
				ObjectListExpressionData listExpr = (ObjectListExpressionData) exprData;
				List<?> list = listExpr.getData();
				for (Object obj : list) {
					Pair<BigDecimal, Date, String, String> result = handleData(obj, maxValue, maxDate, maxDateString, maxString);
					maxValue = result.first;
					maxDate = result.second;
					maxDateString = result.third;
					maxString = result.fourth;
				}
			} else if (exprData instanceof ObjectExpressionData) {
				Object obj = exprData.getData();
				Pair<BigDecimal, Date, String, String> result = handleData(obj, maxValue, maxDate, maxDateString, maxString);
				maxValue = result.first;
				maxDate = result.second;
				maxDateString = result.third;
				maxString = result.fourth;
			} else if (exprData instanceof BindDataListExpressionData) {
				BindDataListExpressionData bindDataList = (BindDataListExpressionData) exprData;
				List<BindData> list = bindDataList.getData();
				for (BindData bindData : list) {
					Object obj = bindData.getValue();
					Pair<BigDecimal, Date, String, String> result = handleData(obj, maxValue, maxDate, maxDateString, maxString);
					maxValue = result.first;
					maxDate = result.second;
					maxDateString = result.third;
					maxString = result.fourth;
				}
			}
		}

		if (maxValue != null) {
			return maxValue;
		} else if (maxDateString != null) {
			return maxDateString; // 返回原始的日期字符串格式
		} else if (maxDate != null) {
			return maxDate; // 如果没有原始字符串格式，返回 Date 对象
		} else if (maxString != null) {
			return maxString; // 返回最大字符串
		} else {
			return null;
		}
	}

	private Pair<BigDecimal, Date, String, String> handleData(Object obj, BigDecimal maxValue, Date maxDate, String maxDateString, String maxString) {
		if (obj == null || (obj instanceof String && StringUtils.isBlank((String) obj))) {
			return new Pair<>(maxValue, maxDate, maxDateString, maxString);
		}

		if (obj instanceof Date) {
			Date date = (Date) obj;
			if (maxDate == null || maxDate.before(date)) {
				maxDate = date;
				maxDateString = null; // 清除之前的字符串格式
			}
		} else if (obj instanceof String) {
			try {
				// 尝试解析为日期时间格式
				Date date = DATE_TIME_FORMAT.parse((String) obj);
				if (maxDate == null || maxDate.before(date)) {
					maxDate = date;
					maxDateString = (String) obj; // 保存原始字符串格式
				}
			} catch (ParseException e) {
				try {
					// 尝试解析为日期格式
					Date date = DATE_FORMAT.parse((String) obj);
					if (maxDate == null || maxDate.before(date)) {
						maxDate = date;
						maxDateString = (String) obj; // 保存原始字符串格式
					}
				} catch (ParseException ex) {
					// 如果解析失败，尝试将其作为数值处理
					try {
						BigDecimal bigData = Utils.toBigDecimal(obj);
						if (bigData != null) {
							if (maxValue == null || maxValue.compareTo(bigData) < 0) {
								maxValue = bigData;
							}
						}
					} catch (com.bstek.ureport.exception.ConvertException ce) {
						// 如果数值解析失败，尝试作为字符串处理
						String str = (String) obj;
						if (maxString == null || maxString.compareTo(str) < 0) {
							maxString = str;
						}
					}
				}
			}
		} else {
			try {
				BigDecimal bigData = Utils.toBigDecimal(obj);
				if (bigData != null) {
					if (maxValue == null || maxValue.compareTo(bigData) < 0) {
						maxValue = bigData;
					}
				}
			} catch (com.bstek.ureport.exception.ConvertException ce) {
				// 如果数值解析失败，尝试作为字符串处理
				String str = obj.toString();
				if (maxString == null || maxString.compareTo(str) < 0) {
					maxString = str;
				}
			}
		}

		return new Pair<>(maxValue, maxDate, maxDateString, maxString);
	}

	@Override
	public String name() {
		return "max";
	}

	// 辅助类，用于封装四个值
	private static class Pair<T1, T2, T3, T4> {
		T1 first;
		T2 second;
		T3 third;
		T4 fourth;

		Pair(T1 first, T2 second, T3 third, T4 fourth) {
			this.first = first;
			this.second = second;
			this.third = third;
			this.fourth = fourth;
		}
	}
}
