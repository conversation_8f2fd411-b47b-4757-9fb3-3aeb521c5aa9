package com.bstek.ureport.expression.function;

import com.bstek.ureport.build.Context;
import com.bstek.ureport.expression.model.data.ExpressionData;
import com.bstek.ureport.expression.model.data.ObjectExpressionData;
import com.bstek.ureport.model.Cell;
import com.bstek.ureport.model.Row;

import java.util.List;


/**
 * <AUTHOR>
*
 * 下钻函数
 */
public class DrillFunction implements Function{
    @Override
    public Object execute(List<ExpressionData<?>> dataList, Context context, Cell currentCell) {
        if (dataList.isEmpty()) {
            return null;
        }
        return null;
    }

    @Override
    public String name() {
        return "drill";
    }
}
