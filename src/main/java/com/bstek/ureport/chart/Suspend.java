package com.bstek.ureport.chart;

public class Suspend {
	
//	private String cellName;
//	
//	private String width;
//	
//	private String height;
//	
//	private String marginLeft;
//	
//	private String marginTop;
//	
//	public String getCellName() {
//		return cellName;
//	}
//
//	public void setCellName(String cellName) {
//		this.cellName = cellName;
//	}
//
//	public String getWidth() {
//		return width;
//	}
//
//	public void setWidth(String width) {
//		this.width = width;
//	}
//
//	public String getHeight() {
//		return height;
//	}
//
//	public void setHeight(String height) {
//		this.height = height;
//	}
//
//	public String getMarginLeft() {
//		return marginLeft;
//	}
//
//	public void setMarginLeft(String marginLeft) {
//		this.marginLeft = marginLeft;
//	}
//
//	public String getMarginTop() {
//		return marginTop;
//	}
//
//	public void setMarginTop(String marginTop) {
//		this.marginTop = marginTop;
//	}
	
	
}
