/*******************************************************************************
 * Copyright 2017 Bstek
 * 
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License.  You may obtain a copy
 * of the License at
 * 
 *   http://www.apache.org/licenses/LICENSE-2.0
 * 
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
 * License for the specific language governing permissions and limitations under
 * the License.
 ******************************************************************************/
package com.bstek.ureport.export;

import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Map;

import com.bstek.cache.CacheUtils;
import com.bstek.common.utils.StringUtils;
import com.bstek.designer.bean.PO.ReportDataPermission;
import com.bstek.designer.mapper.ReportDataPermissionMapper;
import com.bstek.designer.service.ReportDataPermissionService;
import com.bstek.ureport.build.Dataset;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;

import com.bstek.ureport.build.ReportBuilder;
import com.bstek.ureport.definition.CellDefinition;
import com.bstek.ureport.definition.Expand;
import com.bstek.ureport.definition.ReportDefinition;
import com.bstek.ureport.exception.ReportException;
import com.bstek.ureport.exception.ReportParseException;
import com.bstek.ureport.export.builder.down.DownCellbuilder;
import com.bstek.ureport.export.builder.right.RightCellbuilder;
import com.bstek.ureport.model.Report;
import com.bstek.ureport.parser.ReportParser;
import com.bstek.ureport.provider.ProviderFactory;
import com.bstek.ureport.provider.report.ReportProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
*
 */
@Slf4j
@Component
public class ReportRender {

	private ReportParser reportParser = new ReportParser();

	private ReportBuilder reportBuilder = new ReportBuilder();

	private DownCellbuilder downCellParentbuilder = new DownCellbuilder();

	private RightCellbuilder rightCellParentbuilder = new RightCellbuilder();

	@Autowired
	private ReportDataPermissionService reportDataPermissionService;

	@Autowired
	private ReportDataPermissionMapper reportDataPermissionMapper;


	public Report render(String input, String charsetName, Map<String, Object> parameters) {
		ReportDefinition reportDefinition = getReportDefinition(input, charsetName);
		createReport(reportDefinition, parameters);
		return reportBuilder.buildReport(reportDefinition, parameters);
	}
	
	public Report render(String file, Map<String, Object> parameters) {
		ReportDefinition reportDefinition = getReportDefinition(file);
		createReport(reportDefinition, parameters);
		return reportBuilder.buildReport(reportDefinition, parameters);
	}

	public Report render(ReportDefinition reportDefinition, Map<String, Object> parameters) {
		createReport(reportDefinition, parameters);
		return reportBuilder.buildReport(reportDefinition, parameters);
	}

	public Map<String, Dataset> buildDatasetMap(ReportDefinition reportDefinition, Map<String, Object> parameters) {
		createReport(reportDefinition, parameters);
		Map<String, Dataset> datasetMap = reportBuilder.buildDatasets(reportDefinition, parameters);
		return datasetMap;
	}


	public   Map<String, Object>  createReport(ReportDefinition reportDefinition, Map<String, Object> parameters) {
		// 查询当前报表配置的部门字段以及当前用户对应角色的部门
		if (parameters.get("userId") != null && StringUtils.isNotBlank(parameters.get("userId").toString())) {
			List<ReportDataPermission> reportDataPermissions = reportDataPermissionService.
					queryReportDataPermission(reportDefinition.getReportId());
			log.info("####====buildDatasetMap###方法参数reportDataPermissions大小：{}", reportDataPermissions.size());
			String userId = parameters.get("userId").toString();
			List<String> orgIds = reportDataPermissionMapper.queryUserOrgPermission(userId);
			//判断是否是系统管理员角色、系统管理员角色拥有所有数据权限
			List<String> sysUsers = reportDataPermissionMapper.queryAllSystemPermission();
			if (sysUsers.contains(userId)){
				log.info("当前用户ID:{}为系统管理员无需权限控制", userId);
				return parameters;
			}
			log.info("用户：{}，部门：{}，报表 ID：{}", userId, orgIds,  reportDefinition.getReportId());
			if (reportDataPermissions.size() > 0) {
				parameters.put("orgIds", orgIds);
				parameters.put("dataPermissionConf", reportDataPermissions);
			}
		}
			return parameters;
	}



	/**
	 * 通过报表文件获取解析类
	 * @param file
	 * @return
	 */
	public ReportDefinition getReportDefinition(String file) {
		ReportDefinition reportDefinition = parseReport(file);
		rebuildReportDefinition(reportDefinition);
		return reportDefinition;
	}


	public ReportDefinition getReportDefinitionSheet(String file) {
		file = "file:"+file;
		file = file.replace("xlsx","xml");
//		ReportDefinition reportDefinition = CacheUtils.getReportDefinition(file);
        ReportDefinition reportDefinition = null;

		if (reportDefinition == null) {
			reportDefinition = parseReport(file);
			rebuildReportDefinition(reportDefinition);
			CacheUtils.cacheReportDefinition(file, reportDefinition);
		}
		return reportDefinition;
	}

	
	/**
	 * 通过报表内容获取解析类
	 * @param file
	 * @return
	 */
	public ReportDefinition getReportDefinition(String input, String charsetName) {
		InputStream inputStream = IOUtils.toInputStream(input, charsetName);
		ReportDefinition reportDefinition = reportParser.parse(inputStream, "p");
		rebuildReportDefinition(reportDefinition);
		IOUtils.closeQuietly(inputStream);
		return reportDefinition;
	}
	
	

	public void rebuildReportDefinition(ReportDefinition reportDefinition) {
		List<CellDefinition> cells = reportDefinition.getCells();
		for (CellDefinition cell : cells) {
			addRowChildCell(cell, cell);
			addColumnChildCell(cell, cell);
		}
		for (CellDefinition cell : cells) {
			Expand expand = cell.getExpand();
			if (expand.equals(Expand.Down)) {
				downCellParentbuilder.buildParentCell(cell, cells);
			} else if (expand.equals(Expand.Right)) {
				rightCellParentbuilder.buildParentCell(cell, cells);
			}
		}
	}

	public ReportDefinition parseReport(String file) {
		InputStream inputStream = null;
		try {
			inputStream = buildReportFile(file);
			ReportDefinition reportDefinition = reportParser.parse(inputStream, file);
			return reportDefinition;
		} finally {
			try {
				if (inputStream != null) {
					inputStream.close();
				}
			} catch (IOException e) {
				throw new ReportParseException(e);
			}
		}
	}

	private InputStream buildReportFile(String file) {
		ReportProvider provider = ProviderFactory.getReportProvider(file);
		if (provider == null) {
			throw new ReportException("Report [" + file + "] not support.");
		}
		return provider.loadReport(file);
	}

	private void addRowChildCell(CellDefinition cell, CellDefinition childCell) {
		CellDefinition leftCell = cell.getLeftParentCell();
		if (leftCell == null) {
			return;
		}
		List<CellDefinition> childrenCells = leftCell.getRowChildrenCells();
		childrenCells.add(childCell);
		addRowChildCell(leftCell, childCell);
	}

	private void addColumnChildCell(CellDefinition cell, CellDefinition childCell) {
		CellDefinition topCell = cell.getTopParentCell();
		if (topCell == null) {
			return;
		}
		List<CellDefinition> childrenCells = topCell.getColumnChildrenCells();
		childrenCells.add(childCell);
		addColumnChildCell(topCell, childCell);
	}

	public ReportParser getReportParser() {
		return reportParser;
	}

	public ReportBuilder getReportBuilder() {
		return reportBuilder;
	}
}
