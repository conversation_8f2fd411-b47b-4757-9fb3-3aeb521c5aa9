/*******************************************************************************
 * Copyright 2017 Bstek
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License.  You may obtain a copy
 * of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
 * License for the specific language governing permissions and limitations under
 * the License.
 ******************************************************************************/
package com.bstek.ureport.export.html;

import java.util.Collection;
import java.util.List;

import com.bstek.designer.bean.LoadDataVo;
import com.bstek.ureport.chart.ChartData;
import com.bstek.ureport.definition.FloatImage;
import com.bstek.ureport.definition.FreezeCellDefinition;

/**
*
*
 */
public class HtmlReport {

	private String content;

	private String style;

	private String bgImage;

	private int totalPage;

	private int pageIndex;

	private int column;

	private String reportAlign;

	private Collection<ChartData> chartDatas;

	private int htmlIntervalRefreshValue;

	private FreezeCellDefinition freeze;

	private SearchFormData searchFormData;

	private List<FloatImage> floatImages;

	private List<String> sheetNames;

	private String msg;

	private String busiDate; //t-1
	private String yesterDate; //t-2

	private List<LoadDataVo> loadDataVos;

	public List<LoadDataVo> getLoadDataVos() {
		return loadDataVos;
	}

	public void setLoadDataVos(List<LoadDataVo> loadDataVos) {
		this.loadDataVos = loadDataVos;
	}

	public String getYesterDate() {
		return yesterDate;
	}

	public void setYesterDate(String yesterDate) {
		this.yesterDate = yesterDate;
	}

	public String getMsg() {
		return msg;
	}

	public void setMsg(String msg) {
		this.msg = msg;
	}

	public List<String> getSheetNames() {
		return sheetNames;
	}

	public void setSheetNames(List<String> sheetNames) {
		this.sheetNames = sheetNames;
	}

	public String getContent() {
		return content;
	}

	public void setContent(String content) {
		this.content = content;
	}

	public String getStyle() {
		return style;
	}

	public void setStyle(String style) {
		this.style = style;
	}

	public int getTotalPage() {
		return totalPage;
	}

	public void setTotalPage(int totalPage) {
		this.totalPage = totalPage;
	}

	public int getPageIndex() {
		return pageIndex;
	}

	public void setPageIndex(int pageIndex) {
		this.pageIndex = pageIndex;
	}

	public int getColumn() {
		return column;
	}

	public void setColumn(int column) {
		this.column = column;
	}



	public String getBusiDate() {
		return busiDate;
	}

	public void setBusiDate(String busiDate) {
		this.busiDate = busiDate;
	}

	public int getTotalPageWithCol() {
		int totalPageWithCol = totalPage;
		if (column > 0) {
			totalPageWithCol = totalPage / column;
			int m = totalPage % column;
			if (m > 0) {
				totalPageWithCol++;
			}
		}
		return totalPageWithCol;
	}

	public String getReportAlign() {
		return reportAlign;
	}

	public void setReportAlign(String reportAlign) {
		this.reportAlign = reportAlign;
	}

	public Collection<ChartData> getChartDatas() {
		return chartDatas;
	}

	public void setChartDatas(Collection<ChartData> chartDatas) {
		this.chartDatas = chartDatas;
	}

	public int getHtmlIntervalRefreshValue() {
		return htmlIntervalRefreshValue;
	}

	public void setHtmlIntervalRefreshValue(int htmlIntervalRefreshValue) {
		this.htmlIntervalRefreshValue = htmlIntervalRefreshValue;
	}

	public SearchFormData getSearchFormData() {
		return searchFormData;
	}

	public void setSearchFormData(SearchFormData searchFormData) {
		this.searchFormData = searchFormData;
	}

	public String getBgImage() {
		return bgImage;
	}

	public void setBgImage(String bgImage) {
		this.bgImage = bgImage;
	}

	public FreezeCellDefinition getFreeze() {
		return freeze;
	}

	public void setFreeze(FreezeCellDefinition freeze) {
		this.freeze = freeze;
	}

	public List<FloatImage> getFloatImages() {
		return floatImages;
	}

	public void setFloatImages(List<FloatImage> floatImages) {
		this.floatImages = floatImages;
	}
}
