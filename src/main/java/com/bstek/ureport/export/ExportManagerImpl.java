package com.bstek.ureport.export;

import com.bstek.system.mapper.OperationLogMapper;
import com.bstek.ureport.definition.ReportDefinition;
import com.bstek.ureport.export.excel.high.ExcelProducer;
import com.bstek.ureport.export.html.HtmlProducer;
import com.bstek.ureport.export.pdf.PdfProducer;
import com.bstek.ureport.export.word.high.WordProducer;
import com.bstek.ureport.model.Report;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Map;

@Service
@Slf4j
public class ExportManagerImpl implements ExportManager{
    @Autowired
    private ReportRender reportRender;
    private HtmlProducer htmlProducer=new HtmlProducer();
    private WordProducer wordProducer=new WordProducer();
    private ExcelProducer excelProducer=new ExcelProducer();
    private PdfProducer pdfProducer=new PdfProducer();
    @Autowired
    private OperationLogMapper operationLogMapper;


    @Override
    public void exportExcelWithPagingSheet(ExportConfigure config) {
        log.info("ExportManagerImpl#exportExcelWithPagingSheet导出excel文件开始，配置为：{}", config.toString());
        String file=config.getFile();
        Map<String, Object> parameters=config.getParameters();
        for (Map.Entry<String, Object> entry : parameters.entrySet()){
            if ("T-1".equals(entry.getValue())){
                parameters.put(entry.getKey(), operationLogMapper.getBusiDate(LocalDateTime.now().toString()));
            }
            if ("T-2".equals(entry.getValue())){
                parameters.put(entry.getKey(), operationLogMapper.getYesterDate(LocalDateTime.now().toString()));
            }
            if ("当天".equals(entry.getValue())){
                parameters.put(entry.getKey(), com.bstek.common.utils.StringUtils.getTodayDateFormatter(LocalDateTime.now()));
            }
        }
        log.info("ExportManagerImpl#exportExcelWithPagingSheet导出excel文件的参数配置为：{}", parameters);
        ReportDefinition reportDefinition=reportRender.getReportDefinitionSheet(file);
        Report report=reportRender.render(reportDefinition, parameters);
        report.setSheetNames(config.getSheetNames());
        excelProducer.produceWithSheet(report, config.getOutputStream());
        log.info("ExportManagerImpl#exportExcelWithPagingSheet导出excel文件结束");
    }
}
