/*******************************************************************************
 * Copyright 2017 Bstek
 * 
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License.  You may obtain a copy
 * of the License at
 * 
 *   http://www.apache.org/licenses/LICENSE-2.0
 * 
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
 * License for the specific language governing permissions and limitations under
 * the License.
 ******************************************************************************/
package com.bstek.ureport.image;

import java.io.InputStream;

import com.bstek.ureport.exception.ReportComputeException;
import com.bstek.ureport.provider.ProviderFactory;
import com.bstek.ureport.provider.image.ImageProvider;

/**
*
 */
public class StaticImageProcessor implements ImageProcessor<String> {

	@Override
	public InputStream getImage(String path) {
		ImageProvider targetImageProvider = ProviderFactory.getImageProvider(path);
		if (targetImageProvider == null) {
			throw new ReportComputeException("Unsupport image path :" + path);
		}
		try {
			InputStream inputStream = targetImageProvider.getImage(path);
			return inputStream;
		} catch (Exception ex) {
			throw new ReportComputeException(ex);
		}
	}
}
