package com.bstek.designer.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bstek.designer.bean.PO.GroupInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/6/26 17:04
 */
@Mapper
public interface GroupInfoMapper extends BaseMapper<GroupInfo> {

    @Select("select * from r_group_info")
    List<GroupInfo> getAllGroupInfo ();

    @Select("select t3.group_name from r_files t1  \n" +
            "    left join r_file_type_mapping t2 on t1.id=t2.report_id  \n" +
            "    left join  r_group_info  t3 on t2.group_id=t3.id \n" +
            "where t1.report_name = #{reportFullName} ")
    String getGroupName(String reportFullName);


}
