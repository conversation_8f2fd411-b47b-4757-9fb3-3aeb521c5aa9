package com.bstek.designer.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bstek.designer.bean.PO.ReportFiles;
import com.bstek.ureport.provider.report.ReportFile;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 报表文件查询mapper
 * <AUTHOR>
 * @Date 2024/6/25 17:09
 */
@Mapper
public interface ReportFilesMapper extends BaseMapper<ReportFiles> {

    /**
     * 根据报表名查询是否有对应的数据
     * @param fileName
     * @return
     */
    @Select("select * from r_files where file_name = #{fileName}")
    ReportFiles queryReportFilesByName (String fileName);


    List<ReportFiles> queryAllReports (String reportName,String groupId);

    @Select("select * from  r_files where report_name = #{reportName}")
    ReportFiles queryReportFilesByReportName (String reportName);

    @Update("update r_files set publish_menu_name = null where id = #{id}")
    int updatePublishMenuName (String id);



    @Select("select report_name from r_files where file_name = #{reportFullName}")
    String getReportName(String reportFullName);

    @Select("SELECT * FROM BUSI_REP_MAIL WHERE REP_ID LIKE CONCAT('%', #{rid}, '%')")
    List queryMailReportInfo(String rid);

    @Select("select * from r_files where id = #{id}")
    ReportFiles queryReportFilesById (String id);

    List<ReportFiles> queryReportsByUserPermission(String reportName, String groupId, String userId);
    List<ReportFiles> queryAllReportsByUserId(String reportName, String groupId, String userId);
}
