package com.bstek.designer.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bstek.designer.bean.PO.ReportDataPermission;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/2/21 13:30
 */
@Mapper
public interface ReportDataPermissionMapper extends BaseMapper<ReportDataPermission> {
    @Select("\n" +
            "select distinct org_id\n" +
            "from pf_user_role ur left join pf_role_org pro on ur .ROLE_ID  = pro .ROLE_ID  where ur.USER_ID  = #{USER_ID}  and org_id is not null")
    List<String> queryUserOrgPermission(String userId);


    @Select("select t1.id from pf_user  t1 left join pf_user_role t2 on t1.id = t2.user_id where t2.role_id = 'TREEROOT'")
    List<String> queryAllSystemPermission();
}
