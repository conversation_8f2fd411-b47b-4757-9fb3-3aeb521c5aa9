package com.bstek.designer.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bstek.designer.bean.PO.Menu;
import com.bstek.system.bean.po.ResAttrData;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * 菜单mapper
 * <AUTHOR>
 * @Date 2024/6/24 11:03
 */
@Mapper
public interface MenuMapper extends BaseMapper<Menu> {
    /**
     * 获取表中所有的菜单数据
     * @return
     */
    @Select("select * from pf_menu ")
    List<Menu> getAllMenu ();


    @Select("select * from pf_menu where parent_id= #{pid} or parent_id is null  order by ORDERS  + 0 ")
    List<Menu> getAllChildrenByPidIsNull (String pid);

    @Select("select * from pf_menu where parent_id= #{pid}  order by ORDERS  + 0")
    List<Menu> getAllChildrenByPid (String pid);

    @Select("select * from pf_menu where parent_id= '' or  parent_id is null order by ORDERS  + 0")
    List<Menu> getAllRootMenu () ;

    @Select("select * from pf_menu pm  where pm.name = #{reportName}")
    Menu getUreportMenu (String reportName);

    @Select("select name from pf_menu pm  where pm.id = #{menuId}")
    String getMenuName(String menuId);

    @Select("select IS_ADMIN from pf_user where ID= #{userid} ")
    String  queryUserIsAdmin(String userid);

    @Select("select * from pf_menu pm  where pm.ID = #{id}")
    Menu getUreportMenuById(String id);

    @Select({
            "<script>",
            "SELECT * FROM ResAttrData",
            "WHERE attrId = 'role_obj' ",
            "<if test='roleIds != null and roleIds.size() > 0'>",
            "AND attrRecId IN",
            "<foreach item='roleId' collection='roleIds' open='(' separator=',' close=')'>",
            "#{roleId}",
            "</foreach>",
            "</if>",
            "</script>"
    })
    List<ResAttrData> getMenusByUser( @Param("roleIds") String[] roleIds );


    @Select({
            "<script>",
            "SELECT * FROM Menu",
            "WHERE 1=1",
            "<if test='idList != null and idList.size() > 0'>",
            "AND id IN",
            "<foreach item='id' collection='idList' open='(' separator=',' close=')'>",
            "#{id}",
            "</foreach>",
            "</if>",
            "ORDER BY `order`",
            "</script>"
    })
    List<Menu> getMenuIdByFID(@Param("idList") List<String> idList);

    @Select("select max(ORDERS) from pf_menu where PARENT_ID = #{pid}")
    String getOrder(String pid);
}
