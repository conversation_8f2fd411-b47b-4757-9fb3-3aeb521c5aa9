package com.bstek.designer.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bstek.system.bean.po.ResAttrData;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/2/19 15:13
 */
@Mapper
public interface ResAttrDataMapper extends BaseMapper<ResAttrData> {

    @Select("select distinct  ROLE_ID  from pf_Role where ROLE_LEVEL  = 1 ")
    List<String> queryRootLevelRole ();

}
