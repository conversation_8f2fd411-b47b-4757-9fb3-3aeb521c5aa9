package com.bstek.designer.mapper;
import org.apache.ibatis.annotations.Select;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bstek.designer.bean.PO.CustomQuotaPo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface ReportDataMapper{

    @Select("SELECT \n" +
            "    t.Positionname AS positionName,\n" +
            "    t.Dimensionvalue1 AS dimensionValue1,\n" +
            "    t.Department_Name AS departmentName,\n" +
            "    t.Desk1_Name AS desk1Name,\n" +
            "    NVL2(t.Desk2_Name, t.Desk2_Name, '-') AS desk2Name,\n" +
            "    t.Quotaname_Cn AS quotaNameCn,\n" +
            "    t.Quotaname_En AS quotaNameEn,\n" +
            "    '超预警线' AS status,\n" +
            "    t.Equity_Concentration_Ratio AS quotaValue,\n" +
            "    t.Present_Value AS presentValue,\n" +
            "    t.Industry1 AS industry1,\n" +
            "    CASE\n" +
            "        WHEN Threshold1 IS NULL THEN ''\n" +
            "        WHEN a.Quotaname_En IN ('OPTION_MATURITY_NOAS', 'OPTION_MATURITY_AS') THEN\n" +
            "            Threshold1 || Threshold2 || '天'\n" +
            "        WHEN Threshold2 IS NOT NULL AND Threshold3 IS NULL THEN\n" +
            "            Threshold1 || CASE WHEN Threshold2 < 1 AND Threshold2 != 0 THEN '0' END || Threshold2 || '%'\n" +
            "        WHEN Threshold2 IS NULL AND Threshold3 IS NOT NULL THEN\n" +
            "            Threshold1 || CASE WHEN Threshold3 / 100000000 < 1 AND Threshold3 != 0 THEN '0' END || Threshold3 / 100000000 || '亿'\n" +
            "        WHEN Threshold2 IS NOT NULL AND Threshold3 IS NOT NULL THEN\n" +
            "            Threshold1 || 'max(' || CASE WHEN Threshold2 < 1 AND Threshold2 != 0 THEN '0' END || Threshold2 || '%*持仓规模，' || CASE WHEN Threshold3 / 100000000 < 1 AND Threshold3 != 0 THEN '0' END || Threshold3 / 100000000 || '亿)'\n" +
            "    END AS threshold,\n" +
            "    CASE\n" +
            "        WHEN Earlywarmingline1 IS NULL THEN ''\n" +
            "        WHEN a.Quotaname_En IN ('OPTION_MATURITY_NOAS', 'OPTION_MATURITY_AS') THEN\n" +
            "            Earlywarmingline1 || Earlywarmingline2 || '天'\n" +
            "        WHEN Earlywarmingline2 IS NOT NULL AND Earlywarmingline3 IS NULL THEN\n" +
            "            Earlywarmingline1 || CASE WHEN Earlywarmingline2 < 1 AND Earlywarmingline2 != 0 THEN '0' END || Earlywarmingline2 || '%'\n" +
            "        WHEN Earlywarmingline2 IS NULL AND Earlywarmingline3 IS NOT NULL THEN\n" +
            "            Earlywarmingline1 || CASE WHEN Earlywarmingline3 / 100000000 < 1 AND Earlywarmingline3 != 0 THEN '0' END || Earlywarmingline3 / 100000000 || '亿'\n" +
            "        WHEN Earlywarmingline2 IS NOT NULL AND Earlywarmingline3 IS NOT NULL THEN\n" +
            "            Earlywarmingline1 || 'max(' || CASE WHEN Earlywarmingline2 < 1 AND Earlywarmingline2 != 0 THEN '0' END || Earlywarmingline2 || '%*持仓规模，' || CASE WHEN Earlywarmingline3 / 100000000 < 1 AND Earlywarmingline3 != 0 THEN '0' END || Earlywarmingline3 / 100000000 || '亿)'\n" +
            "    END AS earlyWarningLine\n" +
            "FROM \n" +
            "    Etl.Rpt_Rm_Custom_Quota t\n" +
            "LEFT JOIN \n" +
            "    Etl.Rpt_Rm_Custom_Quota_Cfg a\n" +
            "ON \n" +
            "    t.Department_Id = a.Department_Id\n" +
            "    AND t.Desk1_Id = a.Desk1_Id\n" +
            "    AND t.Quotaname_En = a.Quotaname_En\n" +
            "WHERE \n" +
            "    1 = 1  and ROWNUM <= 10 \n")
    List<CustomQuotaPo> getCustomQuotas();

}
