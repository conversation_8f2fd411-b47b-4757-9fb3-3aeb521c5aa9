package com.bstek.designer.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bstek.designer.bean.PO.ReportTypeMapping;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 报表类型映射表mapper
 * <AUTHOR>
 * @Date 2024/6/27 9:29
 */
@Mapper
public interface ReportTypeMappingMapper extends BaseMapper<ReportTypeMapping> {

    @Delete("delete from r_file_type_mapping where group_id = #{groupId} ")
    int deleteReportTypeMappingsByGroupId(String groupId);

    @Select("select * from r_file_type_mapping where report_id = #{reportId}")
   List<ReportTypeMapping> getReportTypeMappingsByReportId (String reportId);

    @Delete("delete from r_file_type_mapping where report_id = #{reportId} ")
    int deleteReportTypeMappingsByReportId (String reportId);

}
