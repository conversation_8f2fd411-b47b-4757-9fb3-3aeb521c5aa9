package com.bstek.designer.action;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bstek.common.bean.Result;
import com.bstek.common.exception.ParamErrorException;
import com.bstek.common.exception.ReportDesignException;
import com.bstek.designer.Utils.HttpClientUtil;
import com.bstek.designer.Utils.PlatFormUtils;
import com.bstek.designer.Utils.ReportUtils;
import com.bstek.designer.bean.DTO.BatchAddPermissionDTO;
import com.bstek.designer.bean.DTO.ReportSendMailDto;
import com.bstek.designer.bean.DTO.ReportWithPermissionsDTO;
import com.bstek.designer.bean.PO.*;
import com.bstek.designer.bean.ReportDefinitionWrapper;
import com.bstek.designer.excel.ExcelParserUtils;
import com.bstek.designer.mapper.*;
import com.bstek.designer.service.IMailService;
import com.bstek.system.action.OperationLogAction;
import com.bstek.system.bean.po.PfUser;
import com.bstek.system.bean.po.ResAttrData;
import com.bstek.system.mapper.OperationLogMapper;
import com.bstek.system.mapper.UserRoleMapper;
import com.bstek.ureport.definition.ReportDefinition;
import com.bstek.ureport.dsl.ReportParserLexer;
import com.bstek.ureport.dsl.ReportParserParser;
import com.bstek.ureport.dsl.ReportParserParser.DatasetContext;
import com.bstek.ureport.exception.IndependenceException;
import com.bstek.ureport.exception.ReportException;
import com.bstek.ureport.export.ReportRender;
import com.bstek.ureport.expression.ErrorInfo;
import com.bstek.ureport.expression.ScriptErrorListener;
import com.bstek.ureport.provider.ProviderFactory;
import com.bstek.ureport.provider.report.ReportFile;
import com.bstek.ureport.provider.report.ReportProvider;
import com.bstek.ureport.provider.report.file.FileReportProvider;
import com.bstek.ureport.utils.StringUtils;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.antlr.v4.runtime.ANTLRInputStream;
import org.antlr.v4.runtime.CommonTokenStream;
import org.antlr.v4.runtime.tree.TerminalNode;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PostConstruct;
import javax.management.relation.Role;
import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.text.Collator;
import java.text.MessageFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 *
 */
@Slf4j
@RestController
@RequestMapping("/designer")
public class DesignerAction {


    public static String reportContent = "<html>\n" +
            "<body>\n" +
            "您好，<br/>\n" +
            "<p>请查收附件中的报告文件，供您参考。非常感谢。<br/></p>\n" +
            "</body>\n" +
            "</html>";


    private final String fileStoreDir = MessageFormat.format("{0}/resource/images", System.getProperty("user.dir"));

    private ReportRender reportRender = new ReportRender();

    @Autowired
    private ReportDataMapper reportDataMapper;

    @Autowired
    private MenuMapper menuMapper;

    @Autowired
    private ReportFilesMapper reportFilesMapper;

    @Autowired
    private GroupInfoMapper groupInfoMapper;

    @Autowired
    private ReportTypeMappingMapper reportTypeMappingMapper;

    @Autowired
    private OperationLogAction operationLogAction;

    @Autowired
    private OperationLogMapper operationLogMapper;

    @Autowired
    private UserRoleMapper userRoleMapper;

    @Autowired
    private JavaMailSenderImpl javaMailSender;

    @Autowired
    private ReportUtils reportUtils;

    @Autowired
    private IMailService mailService;

    @Autowired
    private ReportPermissionMapper reportPermissionMapper;


    @Value("${saveMenuUrl}")
    private String sysUrl;

    @Value("${platformUser}")
    private String userName;

    public static String property = System.getProperty("file.separator");

    @Value("${platformPwd}")
    private String password;

    @Value("${email.from}")
    public String emailFrom;

    @Value("${exportURL}")
    public String postUrl;

    @Value("${reportToken}")
    public String reportToken;

    @Value("${project.static}")
    public String staticPath;

    @Autowired
    private ReportSourceConfigMapper reportSourceConfigMapper;

    @Autowired
    private ResAttrDataMapper resAttrDataMapper;

    private static List<Menu> allRootMenu = new ArrayList<>();

    private static Map<String, Menu> allMenuMap = new HashMap<>();

    private static Map<String, List<Menu>> subMenuMap = new HashMap<>();

    @PostConstruct
    public void initVar() {
        allRootMenu = menuMapper.getAllRootMenu();
        for (Menu m : allRootMenu) {
            // 非叶子菜单。
            if (!"1".equalsIgnoreCase(m.getLeafFlag())) {
                if (StringUtils.isNotBlank(m.getId())) {
                    subMenuMap.put(m.getId(), menuMapper.getAllChildrenByPid(m.getId()));
                } else {
                    subMenuMap.put(m.getId(), menuMapper.getAllChildrenByPidIsNull(m.getId()));
                }

            }

            allMenuMap.put(m.getId(), m);
        }
    }

    /**
 * 查询报表权限列表
 *
 * @param reportId 报表ID
 * @return 包含报表信息和用户权限列表的对象
 */
@RequestMapping("/permission/list")
public Result listReportPermissions(@RequestParam("reportId") String reportId) {
    // 查询报表信息
    LambdaQueryWrapper<ReportFiles> reportQueryWrapper = new LambdaQueryWrapper<>();
    reportQueryWrapper.eq(ReportFiles::getId, reportId);
    ReportFiles reportFiles = reportFilesMapper.selectOne(reportQueryWrapper);
    
    if (reportFiles == null) {
        return new Result("报表不存在", null);
    }
    
    // 查询报表权限列表
    LambdaQueryWrapper<ReportPermission> permissionQueryWrapper = new LambdaQueryWrapper<>();
    permissionQueryWrapper.eq(ReportPermission::getReportId, reportId);
    List<ReportPermission> permissions = reportPermissionMapper.selectList(permissionQueryWrapper);
    
    // 构建用户权限列表
    List<ReportWithPermissionsDTO.UserPermissionDTO> userPermissions = new ArrayList<>();
    for (ReportPermission permission : permissions) {
        // 查询用户信息
        PfUser user = userRoleMapper.selectById(permission.getUserId());
        if (user != null) {
            ReportWithPermissionsDTO.UserPermissionDTO userPermission = 
                new ReportWithPermissionsDTO.UserPermissionDTO(user, permission.getPermissionType());
            userPermissions.add(userPermission);
        }
    }
    
    // 构建返回对象
    ReportWithPermissionsDTO result = new ReportWithPermissionsDTO();
    result.setReportFiles(reportFiles);
    result.setUserPermissions(userPermissions);
    
    return new Result("查询成功", result);
}
 


    /**
     * 批量更新报表权限
     *
     * @param permissionDTO 权限参数封装对象
     * @param request HTTP请求
     * @return 操作结果
     */
    @RequestMapping("/permission/batchAdd")
    public Result batchAddReportPermission(
            @RequestBody BatchAddPermissionDTO permissionDTO,
            HttpServletRequest request) {
        String userId = request.getHeader("Userid");
        
        // 检查报表是否存在
        ReportFiles reportFile = reportFilesMapper.queryReportFilesById(permissionDTO.getReportId());
        if (reportFile == null) {
            return new Result("报表不存在", false);
        }
        
        // 检查是否有权限添加（只有管理员或报表创建者可以添加权限）
        List<String> roleIds = userRoleMapper.getRoleIdsByUserId(userId);
        boolean isAdmin = roleIds.contains("TREEROOT");
        
        if (!isAdmin ) {
            return new Result("没有权限进行此操作", false);
        }
        
        // 批量添加权限
        int successCount = 0;
        //先删除原有权限
        reportPermissionMapper.
        delete(new QueryWrapper<ReportPermission>().eq("report_id", permissionDTO.getReportId()));
        for (String targetUserId : permissionDTO.getUserIds()) {
            // 检查权限是否已存在
            LambdaQueryWrapper<ReportPermission> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ReportPermission::getReportId, permissionDTO.getReportId())
                      .eq(ReportPermission::getUserId, targetUserId);
            int count = reportPermissionMapper.selectCount(queryWrapper);
            if (count > 0) {
                continue; // 跳过已有权限的用户
            }
            
            // 创建新权限
            ReportPermission permission = new ReportPermission();
            permission.setId(IdUtil.simpleUUID());
            permission.setReportId(permissionDTO.getReportId());
            permission.setUserId(targetUserId);
            permission.setPermissionType(permissionDTO.getPermissionType());
            permission.setCreateBy(userId);
            permission.setCreateDate(new Date());
            permission.setUpdateBy(userId);
            permission.setUpdateDate(new Date());
            
            reportPermissionMapper.insert(permission);
            successCount++;
        }
        
        // 记录操作日志
        log.info("批量添加报表权限为" + successCount + "个用户添加报表" + reportFile.getReportName() + "的权限");

        return new Result("成功添加" + successCount + "个用户权限", true);
    }


    /**
     * 表达式校验
     *
     * @param content 表达式内容
     * @return
     */
    @RequestMapping("/scriptValidation")
    public List<ErrorInfo> scriptValidation(String content) {
        content = StringUtils.decode(content);
        ANTLRInputStream antlrInputStream = new ANTLRInputStream(content);
        ReportParserLexer lexer = new ReportParserLexer(antlrInputStream);
        CommonTokenStream tokenStream = new CommonTokenStream(lexer);
        ReportParserParser parser = new ReportParserParser(tokenStream);
        ScriptErrorListener errorListener = new ScriptErrorListener();
        parser.removeErrorListeners();
        parser.addErrorListener(errorListener);
        parser.expression();
        List<ErrorInfo> infos = errorListener.getInfos();
        return infos;
    }

    /**
     * 解析表达式中的数据集
     *
     * @param expr
     * @return
     */
    @RequestMapping("/parseDataSet")
    public Map<String, String> parseDatasetName(String expr) {
        ANTLRInputStream antlrInputStream = new ANTLRInputStream(expr);
        ReportParserLexer lexer = new ReportParserLexer(antlrInputStream);
        CommonTokenStream tokenStream = new CommonTokenStream(lexer);
        ReportParserParser parser = new ReportParserParser(tokenStream);
        parser.removeErrorListeners();

        Map<String, String> result = new HashMap<String, String>();
        DatasetContext ctx = parser.dataset();
        if (ctx != null) {
            TerminalNode node = ctx.Identifier();
            if (node != null) {
                String datasetName = ctx.Identifier().getText();
                result.put("datasetName", datasetName);
            }
        }
        return result;
    }

    /**
     * 加载报表内容
     *
     * @param request
     * @return
     */
    @RequestMapping("/get")
    public ReportDefinitionWrapper loadReport(HttpServletRequest request) {
        log.info("DesignerAction#loadReport加载报表内容开始...");
        String file = request.getParameter("file");
        if (file == null) {
            log.error("DesignerAction#loadReport请求头中缺失参数...");
            throw new ReportDesignException("Report file can not be null.");
        }
        ReportDefinition reportDef = reportRender.parseReport(file);
        reportDef.setBusiDate(operationLogAction.getBusiDate());
        reportDef.setYesterDate(operationLogAction.getYesterDate());
        String reportName = reportFilesMapper.getReportName(reportDef.getReportFullName().replace("file:", ""));
        log.info("DesignerAction#loadReport当前报表名为：{}", reportName);
        LambdaQueryWrapper<ReportFiles> wrapper = new LambdaQueryWrapper();
        wrapper.eq(ReportFiles::getReportName, reportName);
        ReportFiles reportFiles = reportFilesMapper.selectOne(wrapper);
        String reportId = null;
        if (reportFiles != null) {
            reportId = reportFiles.getId();
        }
        reportDef.setReportId(reportId);
        String groupName = groupInfoMapper.getGroupName(reportName);
        reportDef.setReportName(reportName);
        reportDef.setGroupName(groupName);
        return new ReportDefinitionWrapper(reportDef);
    }

    /**
     * 删除报表模板
     *
     * @param request
     * @return
     */
    @RequestMapping("/remove")
    public Result deleteReportFile(HttpServletRequest request, @RequestParam("rid") String rid, @RequestParam("menuId") String menuId) {
        String userid = request.getHeader("Userid");
        String name = operationLogMapper.getUserName(userid);
        String file = request.getParameter("file");
        if (file == null) {
            throw new ReportDesignException("Report file can not be null.");
        }
        //删除报表 需要注意邮件中是否存在
        List mailInfoList = reportFilesMapper.queryMailReportInfo(rid);
        if (!mailInfoList.isEmpty()) {
            return new Result("请先删除在邮件管理中该报表的配置！");
        }
        ReportProvider targetReportProvider = ProviderFactory.getReportProvider(file);
        if (targetReportProvider == null) {
            throw new ReportDesignException("File [" + file + "] not found available report provider.");
        }
        targetReportProvider.deleteReport(file);
        file = file.replace("file:", "");
        String reportName = file.substring(0, file.indexOf(".xml"));
        Menu ureportMenu = null;
        if (StringUtils.isNotBlank(menuId)) {
            this.cancelPublish(menuId, rid, request);
        }
        // 调用平台接口删除对应的菜单数据
//        if (ureportMenu != null) {
//
//        }
        // 删除本身r_files表中的数据
        reportFilesMapper.delete(new QueryWrapper<ReportFiles>().eq("id", rid));
        dealMenuRoleData(menuId,true);
        operationLogAction.writeOperateLog(userid, "灵活报表", "灵活报表/报表发布", "publishReport", "DELETE", "用户：【" + name + "】删除『" + reportName + "』报表", request);

        return new Result("删除成功！");
    }

    @RequestMapping("/delFiles")
    public int deleteReportFiles(HttpServletRequest request) {
        String delFiles = request.getParameter("delFiles");
        if (StringUtils.isBlank(delFiles) || delFiles == null) {
            throw new ReportDesignException("Report file can not be null.");
        }
        ReportProvider targetReportProvider = ProviderFactory.getReportProvider(delFiles);
        if (targetReportProvider == null) {
            throw new ReportDesignException("File [" + delFiles + "] not found available report provider.");
        }
        String[] files = delFiles.split(",");
        for (String file : files) {
            targetReportProvider.deleteReport(file);
        }
        return 1;
    }

    /**
     * 保存报表模板
     *
     * @param reportFile
     * @return
     */
    @RequestMapping("/save")
    public Result saveReportFile(@RequestBody ReportFile reportFile, HttpServletRequest request) {
        String fileName = reportFile.getName();
        String content = reportFile.getContent();
        content = StringUtils.decode(content);
        log.info(content);
        String userid = request.getHeader("Userid");
        String name = operationLogMapper.getUserName(userid);
        ReportDefinition reportDef = reportRender.getReportDefinition(content, "utf-8");
        String reportId = "";
        if (reportDef != null) {

            ReportProvider targetReportProvider = ProviderFactory.getReportProvider(fileName);
            if (targetReportProvider == null) {
                throw new ReportDesignException("File [" + fileName + "] not found available report provider.");
            }
            String oldName = reportFile.getOldName();
            if (oldName.startsWith("classpath:") || !fileName.equals(oldName)) {
                List<ReportFile> files = targetReportProvider.getReportFiles();
                for (ReportFile f : files) {
                    boolean isSameName = f.getName().equals(fileName.replaceFirst(targetReportProvider.getPrefix(), ""));
                    if (isSameName) {
                        throw new ParamErrorException("名称已存在");
                    }
                }
            }
            targetReportProvider.saveReport(fileName, content);

            // 同步到库里
            fileName = fileName.replace("file:", "");
            ReportFiles reportFiles = reportFilesMapper.queryReportFilesByName(fileName);
            boolean isNew = false;
            if (reportFiles == null) {
                isNew = true;
                reportFiles = new ReportFiles();
                reportFiles.setFileName(fileName);
                reportFiles.setCreateDate(new Date());
                reportFiles.setId(UUID.randomUUID().toString());
                reportFiles.setCreateBy(userid);
            }
            reportFiles.setUpdateBy(name);
            reportFiles.setUpdateDate(new Date());
            reportFiles.setReportName(reportFiles.getReportName());
            if (StringUtils.isBlank(reportFiles.getReportName())) {
                reportFiles.setReportName(fileName.substring(0, fileName.indexOf(".xml")));
            }

            if (isNew) {
                reportFilesMapper.insert(reportFiles);
            } else {
                reportFilesMapper.updateById(reportFiles);
            }


            String type = reportFile.getType();
            String[] typeArr = type.split(",");
            // 删除mapping关系
            reportId = reportFiles.getId();
            reportTypeMappingMapper.deleteReportTypeMappingsByReportId(reportId);
            for (String groupId : typeArr) {
                ReportTypeMapping reportTypeMapping = new ReportTypeMapping();
                reportTypeMapping.setId(UUID.randomUUID().toString());
                reportTypeMapping.setGroupId(groupId);
                reportTypeMapping.setReportId(reportId);
                reportTypeMappingMapper.insert(reportTypeMapping);
            }

        }
        return new Result(reportId);
    }

    /**
     * 校验报表名称
     *
     * @param fileName
     * @return
     */
    @PostMapping("/checkFileName")
    public Result checkFileName(@RequestParam("fileName") String fileName) {
        List<ReportFile> reportFiles = new FileReportProvider().getReportFiles();
        boolean present = reportFiles.stream().filter(m -> fileName.equals(m.getName())).findAny().isPresent();
        return new Result(present ? "报表名称重复" : "报表名称不重复", present);
    }


    /**
     * 查看所有报表模板
     *
     * @return
     */
    @RequestMapping("/getList")
    public List<ReportFiles> loadReportProviders(@RequestParam("reportName") String reportName,
                                                 @RequestParam("groupName") String groupName,
                                                 HttpServletRequest request) {
        String userid = request.getHeader("Userid");
        List<String> roleIds = userRoleMapper.getRoleIdsByUserId(userid);
        List<ReportFiles> reportFiles = null;
        if (!roleIds.isEmpty()){
            if (roleIds.contains("TREEROOT")){
                reportFiles = reportFilesMapper.queryAllReports(reportName, groupName);
            }else {
              reportFiles = reportFilesMapper.queryReportsByUserPermission(reportName, groupName, userid);            }
        }
//        List<Menu> allMenu = menuMapper.getAllMenu();
//        List<ReportFiles> reportFiles = reportFilesMapper.queryAllReports(reportName, groupName);
        if (reportFiles == null){
            reportFiles = new ArrayList<>();
        }
        for (ReportFiles reportFile : reportFiles) {

            if (StringUtils.isNotBlank(reportFile.getMenuId())) {
                reportFile.setPublish(true);
            }
            reportFile.setName(reportFile.getFileName());
        }
        return reportFiles;
    }

    /**
     * 导入excel模板
     *
     * @param file
     * @return
     */
    @RequestMapping("/import/excel")
    public ReportDefinitionWrapper importExcel(@RequestParam("file") MultipartFile file) {
        ReportDefinition report = ExcelParserUtils.parser(file);
        if (report != null) {
            report.setReportFullName("classpath:templates/template.xml");
            return new ReportDefinitionWrapper(report);
        }
        throw new RuntimeException("未识别文件");
    }

    /**
     * 导入图片
     *
     * @param file
     * @return
     */
    @RequestMapping("/import/image")
    public int importImage(@RequestParam("file") MultipartFile file) {
        String name = file.getOriginalFilename();
        if (!name.endsWith(".jpg") && !name.endsWith(".jpeg") && !name.endsWith(".png")) {
            throw new RuntimeException("不支持此格式图片");
        }
        File parentFile = new File(fileStoreDir);
        if (!parentFile.exists()) {
            parentFile.mkdirs();
        }
        String fullPath = fileStoreDir + "/" + name;
        try (InputStream inputStream = file.getInputStream();
             FileOutputStream outputStream = new FileOutputStream(new File(fullPath))) {
            IOUtils.copy(inputStream, outputStream);
            return 1;
        } catch (Exception e) {
            throw new ReportException(e);
        }
    }

    /**
     * 查看所有图片
     *
     * @return
     */
    @RequestMapping("/image/files")
    public List<String> getReportFiles(HttpServletRequest request) {
        String scheme = request.getScheme();
        String serverName = request.getServerName();
        int serverPort = request.getServerPort();
        String contextPath = request.getContextPath();
        String path = MessageFormat.format("{0}://{1}:{2,number,#}/resource/images/", scheme, serverName, serverPort);
        if (com.bstek.common.utils.StringUtils.isNotBlank(contextPath)) {
            path = MessageFormat.format("{0}://{1}:{2,number,#}/{3}/resource/images/", scheme, serverName, serverPort, contextPath);
        }
        File file = new File(fileStoreDir);
        if (!file.exists()) {
            file.mkdirs();
        }
        List<String> list = new ArrayList<String>();
        for (File f : file.listFiles()) {
            list.add(path + f.getName());
        }
        return list;
    }


    @GetMapping("/getAllMenu")
    public List<Menu> getMenuTree(HttpServletRequest request) {
        initVar();
        List<Menu> menus = new ArrayList<>();
        menus.addAll(allRootMenu);
        for (Menu m : menus) {
            List<Menu> children = getSubMenu(m.getId());
            List<Menu> sortChildren = new ArrayList<>(children);
            sortChildren.sort((o1, o2) -> {
                if (new Integer(o1.getOrders()) > new Integer(o2.getOrders())) {
                    return 1;
                } else if (new Integer(o1.getOrders()).equals(new Integer(o2.getOrders()))) {
                    return Collator.getInstance(Locale.CHINA).compare(o1.getName(), o2.getName());
                } else {
                    return -1;
                }

            });
            m.setSubmenu(sortChildren);
        }
        return menus;
    }


    private static List getSubMenu(String pid) {
        List<Menu> subMenuList = subMenuMap.get(pid);
        if (subMenuList != null && subMenuList.size() > 0) {
            for (Menu menu : subMenuList) {
                List<Menu> children = getSubMenu(menu.getId());
                List<Menu> sortChildren = new ArrayList<>(children);
                sortChildren.sort((o1, o2) -> {
                    if (new Integer(o1.getOrders()) > new Integer(o2.getOrders())) {
                        return 1;
                    } else if (new Integer(o1.getOrders()).equals(new Integer(o2.getOrders()))) {
                        return Collator.getInstance(Locale.CHINA).compare(o1.getName(), o2.getName());
                    } else {
                        return -1;
                    }

                });
                menu.setSubmenu(sortChildren);
            }
        } else {
            subMenuList = new ArrayList<>();
        }

        return subMenuList;
    }

    // todo 模拟平台保存菜单数据的请求参数

    /**
     * [{"id":"I2c9835c38703f2750187072dd3b00011","name":"日报复核1","img":"ProfileFilled","order":"3","parentId":"I2c9835c38703f2750187072a9a7e0005","pName":"经营&风险日报","type":"page","
     * url":"/dailyManagement/dailyCheck","operationList":"",
     * "operates":"[{\"functionId\":\"RECHECK\",\"functionName\":\"复核\",\"classMethod\":\"DataReviewService/valid;DataReviewService/invalid\"},{\"functionId\":\"QUERY\",\"functionName\":\"查询\",\"classMethod\":\"DataReviewService/queryPage\"}]",
     * "opMenu":"系统管理/菜单管理"}]
     * <p>
     * pid父节点菜单
     */
    @PostMapping("/publishReport")
    public Result publishReport(@RequestParam("reportNames") String reportName, String pid, @RequestParam("url") String url,
                                @RequestParam("publishMenuName") String publishMenuName,
                                @RequestParam("rid") String rid,
                                HttpServletRequest request) {
        // 获取协议，ip，端口
        int result = 1;
        String protocol = request.getProtocol().startsWith("HTTP") ? "http" : "https";
        String localAddr = request.getRemoteAddr();
        int serverPort = request.getRemotePort();
        String token = login();
        String userid = request.getHeader("Userid");
        String name = operationLogMapper.getUserName(userid);

//        if (menuMapper.getUreportMenu(reportName) != null) {
//            return new Result("【" + reportName + "】报表发布失败，当前报表已发布", 0);
//        }
        // 拼接系统的url
//			String url = protocol + "://" + localAddr + ":" + serverPort  + "/ureport/html/" + reportName + ".ureport.xml";
//			log.info("待发布菜单的url如下：" + url);
        //获取菜单最大排序 默认+1
        String maxOrder = menuMapper.getOrder(pid);
        String order = (maxOrder == null ? "99" : String.valueOf(Integer.parseInt(maxOrder) + 1));
        Map<String, String> map = new HashMap<>();
        String uuid = IdUtil.fastSimpleUUID();
        map.put("id", uuid);
        map.put("name", reportName);
        map.put("img", "");
        map.put("order", order);
        map.put("parentId", pid);
        map.put("pName", "");
        map.put("type", "page");
        map.put("url", url);
        map.put("operationList", "");
        map.put("publishReport", "true");
        result = PlatFormUtils.saveMenu(sysUrl, JSON.toJSON(Arrays.asList(map)).toString(), token);
        operationLogAction.writeOperateLog(userid, "灵活报表", "灵活报表/报表发布", "publishReport", "PUBLISH", "用户：【" + name + "】在菜单 【" + publishMenuName + "】下发布『" + reportName + "』报表发布成功", request);
        UpdateWrapper<ReportFiles> wrapper = new UpdateWrapper<>();
        wrapper.eq("id", rid);
        wrapper.set("parent_id", pid);
        wrapper.set("menu_id", uuid);
        wrapper.set("publish_menu_name", publishMenuName);
        wrapper.set("update_by", name);
        reportFilesMapper.update(null, wrapper);
        // 添加权限问题 默认给[系统管理员]勾上对应菜单
        dealMenuRoleData(uuid, false);
        return new Result(result == 1 ? "发布成功" : "发布失败", result);
    }

    /**
     * 发布一个菜单后,系统管理员的菜单权限自动出现发布菜单的权限
     * @param menuId -- 菜单ID
     * @param isDel -- 是否删除,false为插入,true为删除
     */
    private void dealMenuRoleData (String menuId, boolean isDel) {
        log.info("DesignerAction.dealMenuRoleData开始处理发布菜单后的角色数据,当前处理逻辑为删除与否{}", isDel);
        if (isDel) {
            log.info("DesignerAction.dealMenuRoleData开始删除发布菜单后的角色数据");
            resAttrDataMapper.delete(new QueryWrapper<ResAttrData>().eq("res_rec_id", menuId).eq("attr_id", "role_obj"));
            return;
        }
        List<String> rootRoleIds = resAttrDataMapper.queryRootLevelRole();
        log.info("当前角色树层级为1的节点ID有{}个", rootRoleIds.size());
        for (String rootRoleId : rootRoleIds) {
            ResAttrData resAttrData = ResAttrData.builder()
                    .resId(menuId + "resource").attrId("role_obj").resRecId(menuId).attrRecId(rootRoleId).build();
            resAttrDataMapper.insert(resAttrData);
        }
    }



    /**
     * 取消发布报表 --- 删除菜单数据
     *
     * @param menuId
     * @return
     */
    @PostMapping("/cancelPublish")
    public Result cancelPublish(@RequestParam("menuId") String menuId, @RequestParam("rid") String rid, HttpServletRequest request) {
        int result = 1;
        String userid = request.getHeader("Userid");
        String name = operationLogMapper.getUserName(userid);
        String token = login();
        Map<String, String> params = new HashMap<>(2);
        params.put("id", menuId);
        Arrays.asList(params);
        String reportName = menuMapper.getMenuName(menuId);
        result = PlatFormUtils.delMenu(sysUrl, JSON.toJSON(Arrays.asList(params)).toString(), token);
        UpdateWrapper<ReportFiles> wrapper = new UpdateWrapper<ReportFiles>();
        wrapper.eq("id", rid);
        wrapper.set("publish_menu_name", null);
        wrapper.set("parent_id", null);
        wrapper.set("menu_id", null);
        wrapper.set("update_by", name);
        reportFilesMapper.update(null, wrapper);
        dealMenuRoleData(menuId, true);
        operationLogAction.writeOperateLog(userid, "灵活报表", "灵活报表/报表发布", "publishReport", "CANCELPUBLISH", "用户：【" + name + "】取消发布『" + reportName + "』报表", request);
        return new Result(result == 1 ? "取消发布成功" : "取消发布失败", result);
    }


    private String login() {
        Map<String, String> paramsMap = new HashMap<>(4);
        paramsMap.put("userName", userName);
        paramsMap.put("password", password);
        String token = PlatFormUtils.login(sysUrl, JSON.toJSON(Arrays.asList(paramsMap)).toString());
        return token;
    }


    /**
     * 保存或者修改分组信息
     *
     * @return
     */
    @PostMapping("/saveOrUpdateGroup")
    public Result saveOrUpdateGroup(@RequestBody GroupInfo groupInfo) {
        if (groupInfoMapper.getAllGroupInfo().stream()
                .anyMatch(m -> m.getGroupName().equals(groupInfo.getGroupName()))) {
            return new Result("保存失败", "");
        }
        // 修改
        int result = 0;
        String id = groupInfo.getId();
        if (StringUtils.isNotBlank(id)) {
            result = groupInfoMapper.updateById(groupInfo);
        } else {
            // 新增
            id = UUID.randomUUID().toString();
            groupInfo.setId(id);
            result = groupInfoMapper.insert(groupInfo);
        }
        return new Result(result == 1 ? "保存成功" : "保存失败", id);
    }

    /**
     * 删除分组信息
     *
     * @param id
     * @return
     */
    @DeleteMapping("/delGroupInfo/{id}")
    public Result delGroupInfo(@PathVariable("id") String id) {
        int result = groupInfoMapper.deleteById(id);
        // 级联删除mapping信息
        reportTypeMappingMapper.deleteReportTypeMappingsByGroupId(id);
        return new Result(result == 1 ? "删除成功" : "删除失败", result);
    }


    /**
     * 修改报表类型
     *
     * @param reportId
     * @param groupIds
     * @return
     */
    @PostMapping("/updateReportFileType")
    public Result updateReportFileType(@RequestParam("reportId") String reportId, @RequestParam("groupIds") String groupIds) {
        // 根据报表id查询某张报表的所有mappings
        int result = 0;
        reportTypeMappingMapper.deleteReportTypeMappingsByReportId(reportId);
        String[] groupArr = groupIds.split(",");
        for (String groupId : groupArr) {
            ReportTypeMapping reportTypeMapping = new ReportTypeMapping();
            reportTypeMapping.setId(UUID.randomUUID().toString());
            reportTypeMapping.setGroupId(groupId);
            reportTypeMapping.setReportId(reportId);
            result = reportTypeMappingMapper.insert(reportTypeMapping);
        }
        return new Result(result == 1 ? "更新成功" : "更新失败", result);
    }


    @GetMapping("/getGroupInfo")
    public Result getGroupInfo() {
        List<GroupInfo> allGroupInfo = groupInfoMapper.getAllGroupInfo();
        return new Result(allGroupInfo);
    }


    @GetMapping("/getIP")
    public Result getIP(HttpServletRequest request) {
        return new Result(request.getRemoteAddr());
    }


    @PostMapping("/updateReport")
    public Result updateReport(@RequestParam("id") String id, @RequestParam("reportName") String reportName) {
        if (StringUtils.isEmpty(reportName)) {
            return new Result("修改失败,无效报表名", "");
        }
        QueryWrapper<ReportFiles> wrapper = new QueryWrapper<ReportFiles>();
        wrapper.eq("report_name", reportName);
        List<ReportFiles> reportFiles = reportFilesMapper.selectList(wrapper);
        if (!reportFiles.isEmpty()) {
            return new Result("修改失败,报表名重复!", "");
        }
        QueryWrapper wrapperOne = new QueryWrapper<ReportFiles>();
        wrapperOne.eq("id", id);
        ReportFiles selectOne = reportFilesMapper.selectOne(wrapperOne);
        String menuId = selectOne.getMenuId();
        if (menuId != null && !"".equals(menuId)) {
            UpdateWrapper<Menu> menuWrapper = new UpdateWrapper<Menu>();
            menuWrapper.eq("id", menuId);
            menuWrapper.set("name", reportName);
            menuMapper.update(null, menuWrapper);
        }


        UpdateWrapper<ReportFiles> upDatewrapper = new UpdateWrapper<ReportFiles>();
        upDatewrapper.eq("id", id);
        upDatewrapper.set("report_name", reportName);
        return new Result(reportFilesMapper.update(null, upDatewrapper) == 1 ? "修改成功" : "修改失败", "");
    }

    @PostMapping("/reportList")
    @DS("slave")
    public Result reportList() {
        List<CustomQuotaPo> customQuotas = reportDataMapper.getCustomQuotas();
        return new Result("查询成功！", customQuotas);
    }

    @PostMapping("/sendMail")
    public Result sendMailReport(@RequestBody ReportSendMailDto reportSendMailDto, HttpServletRequest request) throws IOException {
        String userid = request.getHeader("Userid");
        String name = operationLogMapper.getUserName(userid);
        ReportFiles busiRepInfo = reportFilesMapper.queryReportFilesById(reportSendMailDto.getReportId());
        String attachName = busiRepInfo.getReportName() + ".xlsx";
        Map<String, Object> requestParams = new HashMap<>();
        requestParams.put("reportName", busiRepInfo.getFileName());
        requestParams.put("reportId", reportSendMailDto.getReportId());
        requestParams.put("content", "");
        Map<String, String> query = new HashMap<>();
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime shanghaiTime = now.atZone(ZoneId.of("Asia/Shanghai")).toLocalDateTime();
        // 定义日期格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        DateTimeFormatter formatter_ = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        // 格式化日期
        String formattedDate = shanghaiTime.format(formatter);

        //yyyyMMdd T-1
        String busiDate = operationLogMapper.getBusiDate(formattedDate);
        //yyyy-mm-dd  T-1
        String busiDate_ = LocalDate.parse(busiDate, formatter).format(formatter_);
        //yyyyMMdd T-2
        String yesterDate = operationLogMapper.getYesterDate(formattedDate);
        //yyyy-mm-dd  T-2
        String yesterDate_ = LocalDate.parse(yesterDate, formatter).format(formatter_);


//        //yyyyMMdd T-1
//        query.put("date",busiDate);
//        query.put("analysisdate",busiDate);
//        query.put("reportdate",busiDate);
//        query.put("ANALYSISDATE",busiDate);
//        //yyyy-mm-dd  T-1
//        query.put("date_pre",busiDate_);
//        // yyyymmdd 当天
//        query.put("today",formattedDate);
//        //yyyy-mm-dd  当天
//        query.put("to_day",shanghaiTime.format(formatter_));
//        // yyyymmdd T-2
//        query.put("yesterDate",yesterDate);
//        //yyyy-mm-dd  T-2
//        query.put("yesterDate_pre",yesterDate_);

//        query = reportUtils.getDataSourceInfo("file:" + busiRepInfo.getFileName());
        query = reportUtils.getDataSourceInfo("s3:" + busiRepInfo.getFileName());
        //需要记录最小日期
        String miniDate =  "";
        for (Map.Entry<String, String> entry : query.entrySet()) {
            if ("当天".equals(entry.getValue())) {
                entry.setValue(formattedDate);
                miniDate = formattedDate;
            } else if ("T-1".equals(entry.getValue())) {
                entry.setValue(busiDate);
                miniDate = busiDate;
            } else if ("T-2".equals(entry.getValue())) {
                entry.setValue(yesterDate);
                miniDate = yesterDate;
            }
        }
        List<String> userList =reportSendMailDto.getReviceId();
        List<String> emailList = reportSendMailDto.getRecipientMail();
        attachName = "【"
        for (int i = 0; i < userList.size(); i++){
            String filePath = staticPath + "Report" + property + "temp"+property+formattedDate
                    + property +UUID.randomUUID() + property+ busiRepInfo.getReportName() + ".xlsx";
            query.put("userId", userList.get(i));
            query.put("roleId", "TREEROOT");
            requestParams.put("query", query);
            // 使用 Gson 将 Map 转换为 JSON 字符串
            log.info("参数为：{}", requestParams);
            int finalI = i;

            sendMail(emailList.get(finalI), requestParams, filePath, attachName, busiRepInfo);

        }
        operationLogAction.writeOperateLog(userid, "灵活报表", "灵活报表/报表发布", "publishReport", "PUBLISH", "用户：【" + name + "】发送『" + busiRepInfo.getReportName() + "』报表邮件 ", request);
        return new Result("发送成功！");
    }

    public void sendMail(String recipientMail, Map<String, Object> requestParams, String filePath, String attachName, ReportFiles busiRepInfo) throws IOException {
        try {
            Gson gson = new Gson();
            String jsonRequest = gson.toJson(requestParams);
            log.info("参数为：{}", jsonRequest);
            HttpClientUtil.downloadReport(postUrl, filePath, reportToken, jsonRequest);
            List<String> mails = Arrays.asList(recipientMail.split(","));
            if (mails.isEmpty()) {
                log.error("邮箱系统====报错！缺失收件人！");
            }
            for (String mail : mails) {
                mailService.sendHtmlMailWithAttachments(mail, busiRepInfo.getReportName() + " 报表邮件", reportContent, Arrays.asList(filePath),Arrays.asList(attachName));
//                    new EmailUtil().sendReportFileAsEmail(Arrays.asList(mail), emailFrom, attachName, filePath, busiRepInfo.getReportName() + " 报表邮件", reportContent, javaMailSender);
            }
//            FileUtil.del(filePath);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }



@PostMapping("getAllUser")
public Result getAllUser(@RequestParam("userName") String userName, @RequestParam("start") Integer start, @RequestParam("limit") Integer limit) {
    QueryWrapper<PfUser> queryWrapper = new QueryWrapper<>();
    Page<PfUser> page = new Page<>(start, limit);
    if (!userName.isEmpty()) {
        queryWrapper.like("user_name", userName);
    }
    userRoleMapper.selectPage(page, queryWrapper);

    Map<String, Object> result = new HashMap<>();
    result.put("total", page.getTotal());
    result.put("rows", page.getRecords());

    // 返回分页结果
    return new Result(result);

}

/**
 * 保存sheet页名称
 *
 * @param args
 */
@PostMapping("/saveSheetName")
public Result saveSheetName(@RequestParam("reportId") String reportId, @RequestParam("sheetNames") String sheetNames) {
    LambdaQueryWrapper<ReportFiles> wrapper = new LambdaQueryWrapper<>();
    wrapper.eq(ReportFiles::getId, reportId);
    ReportFiles reportFiles = reportFilesMapper.selectOne(wrapper);
    reportFiles.setSheetNames(sheetNames);
    reportFilesMapper.updateById(reportFiles);
    return new Result("保存成功！");
}


@PostMapping("/saveDataSourceConfig")
public Result saveDataSourceConfig(@RequestBody ReportSourceConfig config) {
    LambdaQueryWrapper<ReportSourceConfig> wrapper1 = new LambdaQueryWrapper<>();
    wrapper1.eq(ReportSourceConfig::getCode, config.getCode());
    Integer i = reportSourceConfigMapper.selectCount(wrapper1);
    if (i > 10) {
        throw new IndependenceException("超过默认数据集数量！");
    }
    LambdaQueryWrapper<ReportSourceConfig> wrapper2 = new LambdaQueryWrapper<>();
    wrapper2.eq(ReportSourceConfig::getCode, config.getCode());
    wrapper2.eq(ReportSourceConfig::getName, config.getName());
    wrapper2.eq(ReportSourceConfig::getDataName, config.getDataName());
    ReportSourceConfig old = reportSourceConfigMapper.selectOne(wrapper2);
    if (old == null) {
        ReportSourceConfig reportSourceConfig = new ReportSourceConfig();
        reportSourceConfig.setId(cn.hutool.core.lang.UUID.randomUUID().toString());
        reportSourceConfig.setCode(config.getCode());
        reportSourceConfig.setName(config.getName());
        reportSourceConfig.setData(config.getData());
        reportSourceConfig.setDataName(config.getDataName());
        reportSourceConfigMapper.insert(reportSourceConfig);
        return new Result("保存成功！");
    }
    throw new IndependenceException("重复存在！");
}

@PostMapping("/selectSourceConfg")
public Result selectSourceConfg(@RequestBody List<ReportSourceConfig> configs) {
    Map<String, List<ReportSourceConfig>> map = new HashMap<>();
    for (ReportSourceConfig config : configs) {
        String id = config.getId();
        String name = config.getName();
        String code = config.getCode();
        LambdaQueryWrapper<ReportSourceConfig> wrapper = new LambdaQueryWrapper<>();
        if (code != null && !"".equals(code)) {
            wrapper.eq(ReportSourceConfig::getCode, code);
            List<ReportSourceConfig> reportSourceConfigList = reportSourceConfigMapper.selectList(wrapper);
            if (reportSourceConfigList != null && reportSourceConfigList.size() > 0) {
                ReportSourceConfig reportSourceConfig = reportSourceConfigList.get(0);
                map.put(reportSourceConfig.getName(), reportSourceConfigList);

            }
        } else if (code != null && name != null && !"".equals(name)) {
            wrapper.eq(ReportSourceConfig::getName, name);
            List<ReportSourceConfig> reportSourceConfigs = reportSourceConfigMapper.selectList(wrapper);
            //拿到reportSourceConfigs里的对象ReportSourceConfig的第一个code
            map.put(name, reportSourceConfigs);
        }
    }
    return new Result(map);
}


public static void main(String[] args) {
    LocalDateTime now = LocalDateTime.now();
    LocalDateTime shanghaiTime = now.atZone(ZoneId.of("Asia/Shanghai")).toLocalDateTime();

    // 定义日期格式
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");

    // 格式化日期
    String formattedDate = shanghaiTime.format(formatter);
    System.out.println(formattedDate);
    DateTimeFormatter formatter_ = DateTimeFormatter.ofPattern("yyyy-MM-dd");


    //yyyy-mm-dd  T-1
    String busiDate_ = LocalDate.parse(formattedDate, formatter).format(formatter_);

    //yyyy-mm-dd  T-2
    String yesterDate_ = LocalDate.parse(formattedDate, formatter).format(formatter_);
    System.out.println(yesterDate_);
    System.out.println(busiDate_);
}
}
