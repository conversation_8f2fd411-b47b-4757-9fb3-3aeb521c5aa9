/*******************************************************************************
 * Copyright 2017 Bstek
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License.  You may obtain a copy
 * of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
 * License for the specific language governing permissions and limitations under
 * the License.
 ******************************************************************************/
package com.bstek.designer.action;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bstek.common.bean.Result;
import com.bstek.designer.bean.LoadDataVo;
import com.bstek.designer.bean.PO.ReportCheckSource;
import com.bstek.designer.bean.PO.ReportDataPermission;
import com.bstek.designer.bean.PO.ReportFiles;
import com.bstek.designer.mapper.ReportCheckSourceMapper;
import com.bstek.designer.mapper.ReportDataPermissionMapper;
import com.bstek.designer.mapper.ReportFilesMapper;
import com.bstek.designer.service.ReportCheckSourceService;
import com.bstek.designer.service.ReportDataPermissionService;
import com.bstek.system.action.OperationLogAction;
import com.bstek.system.mapper.OperationLogMapper;
import com.bstek.ureport.build.Dataset;
import com.bstek.ureport.definition.dataset.DatasetDefinition;
import com.bstek.ureport.definition.dataset.Parameter;
import com.bstek.ureport.definition.dataset.SqlDatasetDefinition;
import com.bstek.ureport.definition.datasource.BuildinDatasourceDefinition;
import com.bstek.ureport.definition.datasource.DataType;
import com.bstek.ureport.definition.datasource.DatasourceDefinition;
import com.bstek.ureport.definition.searchform.Component;
import com.bstek.ureport.definition.searchform.DateInputComponent;
import com.bstek.ureport.definition.searchform.SearchForm;
import com.bstek.ureport.export.*;
import com.bstek.ureport.model.Cell;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.bstek.common.exception.ReportDesignException;
import com.bstek.common.utils.StringUtils;
import com.bstek.designer.bean.PreviewPageParameters;
import com.bstek.designer.bean.PreviewParameters;
import com.bstek.designer.bean.PreviewSearchForm;
import com.bstek.shiro.JWTUtil;
import com.bstek.shiro.TokenFilter;
import com.bstek.ureport.Utils;
import com.bstek.ureport.build.paging.Page;
import com.bstek.ureport.definition.Paper;
import com.bstek.ureport.definition.ReportDefinition;
import com.bstek.ureport.exception.ReportComputeException;
import com.bstek.ureport.exception.ReportException;
import com.bstek.ureport.export.html.HtmlProducer;
import com.bstek.ureport.export.html.HtmlReport;
import com.bstek.ureport.export.html.SearchFormData;
import com.bstek.ureport.model.Report;
import com.bstek.ureport.provider.ProviderFactory;
import com.bstek.ureport.provider.report.ReportProvider;
import com.github.benmanes.caffeine.cache.Cache;

/**
*
*
 */
@RestController
@RequestMapping("/ureport")
@Slf4j
public class ReportPreviewAction{

	@Resource
	private Cache<String, Report> caffeineCache;

	@Resource
	private ReportFilesMapper reportFilesMapper;

	@Autowired
	private ReportRender reportRender;

	private HtmlProducer htmlProducer = new HtmlProducer();

	@Resource
	private OperationLogMapper operationLogMapper;

	@Resource
	private ExportManagerImpl exportManager;

	@Resource
	private ReportCheckSourceMapper reportCheckSourceMapper;

	@Autowired
	private ReportCheckSourceService reportCheckSourceService;


	@Resource
	private OperationLogAction operationLogAction;



	@Autowired
	private ReportDataPermissionService reportDataPermissionService;


	@Autowired
	private ReportDataPermissionMapper reportDataPermissionMapper;



	@RequestMapping("/loadData")
	@ResponseBody
	public HtmlReport loadData(HttpServletRequest request, @RequestBody PreviewPageParameters pageParameters) {
		log.info("ReportPreviewAction#loadData加载数据开始：参数为：{}", pageParameters.toString());
		long start = System.currentTimeMillis();
		PreviewParameters params = pageParameters.getParams();
		//方便给邮件获取HTML
		if (params.getReportName().isEmpty() && params.getReportId()!=null && !params.getReportId().isEmpty()) {
			LambdaQueryWrapper<ReportFiles> wrapper = new LambdaQueryWrapper<>();
			wrapper.eq(ReportFiles::getId, params.getReportId());
			ReportFiles reportFiles = reportFilesMapper.selectOne(wrapper);
			params.setReportName(reportFiles.getFileName());
		}
		ReportDefinition reportDefinition = getReportDefinition(request, params);
		Map<String, Object> parameters = params.getQuery();
		String reportId = pageParameters.getReportId();
		HtmlReport htmlReport = new HtmlReport();
		Paper paper = reportDefinition.getPaper();
		Report report = null;
		String html = "";
		if (paper.isPageEnabled()) { // 分页
			String reportName = params.getReportName();
			// 通过读取文件分页，需要判断文件是否被修改，防止加载修改前的缓存数据
			if (StringUtils.isNotBlank(reportName)) {
				ReportProvider reportProvider = ProviderFactory.getFileReportProvider();
				try (InputStream in = reportProvider.loadReport("file:" + reportName)){
					String uuid = DigestUtils.md5Hex(in);
					params.setSign(uuid);
				} catch (IOException e) {
					log.error("读取文件失败", e);
				}
			}
			String key = DigestUtils.md5Hex(params.toString());
			report = caffeineCache.getIfPresent(key);
			if (report == null) {
				parameters = createParam(reportDefinition, parameters);
				report = reportRender.render(reportDefinition, parameters);
				//caffeineCache.put(key, report);
			}
			int pageIndex = pageParameters.getPageIndex();
			SinglePageData pageData = PageBuilder.buildSinglePageData(pageIndex, report);
			List<Page> pages = pageData.getPages();
			html = htmlProducer.produce(report.getContext(), pages, pageData.getColumnMargin(), false);
			htmlReport.setTotalPage(pageData.getTotalPages());
			htmlReport.setPageIndex(pageIndex);
			if (reportDefinition.getPaper().isColumnEnabled()) {
				htmlReport.setColumn(reportDefinition.getPaper().getColumnCount());
			}
			LambdaQueryWrapper<ReportFiles> wrapper = new LambdaQueryWrapper<>();
			wrapper.eq(ReportFiles::getId,reportId);
			ReportFiles reportFiles = reportFilesMapper.selectOne(wrapper);
			String sheetNames = null;
			if (reportFiles != null){
				sheetNames = reportFiles.getSheetNames();
			}

			if (sheetNames == null){
				List<String> sheetNameList = new ArrayList<>();
				for (int i=0;i<pageData.getTotalPages();i++){
					sheetNameList.add("sheet"+(i+1));
				}
				htmlReport.setSheetNames(sheetNameList);
			}else {
				htmlReport.setSheetNames(Arrays.asList(sheetNames.split(",")));
			}
		} else {
			parameters = createParam(reportDefinition, parameters);
			report = reportRender.render(reportDefinition, parameters);
			html = htmlProducer.produce(report);
//			System.out.println(html);
		}
		htmlReport.setMsg(report.getMsg());
		htmlReport.setChartDatas(report.getContext().getChartDataMap().values());
		htmlReport.setContent(html);
		htmlReport.setStyle(reportDefinition.getStyle());
		SearchFormData searchFormData = reportDefinition.buildSearchFormData(report.getContext().getDatasetMap(),parameters);
		htmlReport.setSearchFormData(searchFormData);
		htmlReport.setReportAlign(report.getPaper().getHtmlReportAlign().name());
		htmlReport.setBgImage(report.getPaper().getBgImage());
		htmlReport.setFreeze(reportDefinition.getFreeze());
		htmlReport.setFloatImages(reportDefinition.getFloatImages());
		htmlReport.setBusiDate(operationLogAction.getBusiDate());
		htmlReport.setYesterDate(operationLogAction.getYesterDate());
		htmlReport.setLoadDataVos(initLoadDataList(report));
		long end = System.currentTimeMillis();
		log.info("加载报表耗时：" + (end - start)/1000 + "秒");;
		log.info("ReportPreviewAction#loadData加载数据结束...");
		return htmlReport;
	}


	/**
	 * 查询当前报表的查询条件
	 * @return
	 */
	@RequestMapping("/querySearchFormData")
	@ResponseBody
	public SearchFormData querySearchFormData (HttpServletRequest request, @RequestBody PreviewPageParameters pageParameters) {
		log.info("ReportPreviewAction#querySearchFormData构建当前报表的查询条件开始...");
		PreviewParameters params = pageParameters.getParams();
		ReportDefinition reportDefinition = getReportDefinition(request, params);
		Map<String, Object> parameters = params.getQuery();
		Map<String, Dataset> datasetMap = reportRender.buildDatasetMap(reportDefinition, parameters);
		SearchFormData searchFormData = reportDefinition.buildSearchFormData(datasetMap,parameters);
		log.info("ReportPreviewAction#querySearchFormData构建当前报表的查询条件结束");
		return searchFormData;
	}

	/**
	 * 初始化每个sheet页面的html数据
	 * @param report 报表对象
	 * @return
	 */
	private List<LoadDataVo> initLoadDataList(Report report) {
		// 总页数
		List<LoadDataVo> loadDataList = new ArrayList<>();
		if (report == null) {
			return loadDataList;
		}
		int totalPageSize = report.getPages().size();
		if (totalPageSize > 0) {
			for (int i = 1; i <= totalPageSize; i++) {
				SinglePageData pageData = PageBuilder.buildSinglePageData(i, report);
				List<Page> pages = pageData.getPages();
				String html = htmlProducer.produce(report.getContext(), pages, pageData.getColumnMargin(), false);
				LoadDataVo loadDataVo = new LoadDataVo(String.valueOf(i), html);
				loadDataList.add(loadDataVo);
			}
		}
		return loadDataList;
	}
	@PostMapping("/checkReportIsNormal") // 校验报表是否正常
	public Result checkReportIsNormal (HttpServletRequest request, @RequestBody PreviewPageParameters pageParameters) {
		Map<String,String> resultMap = new HashMap<>(4);
		try {
			HtmlReport htmlReport = this.loadData(request, pageParameters);
			return StringUtils.isNotBlank(htmlReport.getMsg()) ?new Result(htmlReport.getMsg()) : new Result();
		}catch (Exception e) {
			log.error("ReportPreviewAction#loadData加载数据异常", e);
			return new Result(e.getMessage());
		}
	}

	@PostMapping("/checkReportIsAbnormal") // 校验数据集中是否有异常邮件
	public Result checkReportIsAbnormal (HttpServletRequest request, @RequestBody PreviewPageParameters pageParameters) {
		log.info("ReportPreviewAction#checkReportIsAbnormal开始校验报表是否存在异常，参数为：{}", pageParameters );
		// 是否异常
		boolean isAbnormal = false;
		PreviewParameters params = pageParameters.getParams();
		if (params.getReportName().isEmpty() && params.getReportId()!=null && !params.getReportId().isEmpty()) {
			LambdaQueryWrapper<ReportFiles> wrapper = new LambdaQueryWrapper<>();
			wrapper.eq(ReportFiles::getId, params.getReportId());
			ReportFiles reportFiles = reportFilesMapper.selectOne(wrapper);
			params.setReportName(reportFiles.getFileName());
		}
		ReportDefinition reportDefinition = getReportDefinition(request, params);
		Map<String, Object> parameters = params.getQuery();
		Report report = reportRender.render(reportDefinition, parameters);
		List<Cell> lazyComputeCells = report.getLazyComputeCells();
		log.info("ReportPreviewAction#checkReportIsAbnormal校验的单元格个数为：{}", lazyComputeCells.size());
		isAbnormal = lazyComputeCells.stream().filter(m -> m.isConditionSatisfy()).collect(Collectors.toList()).size() > 0;
		log.info("ReportPreviewAction#checkReportIsAbnormal校验结果为：{}", isAbnormal);
		return new Result(isAbnormal);
	}

	@PostMapping("/checkReportIsWarmLimit") // 校验数据集中是否有异常邮件
	public Result checkReportIsWarmLimit (HttpServletRequest request, @RequestBody PreviewPageParameters pageParameters) {
		log.info("ReportPreviewAction#checkReportIsWarmLimit开始校验报表是否存在异常，参数为：{}", pageParameters );
		// 是否异常
		boolean isWarm = false;
		boolean isLimit = false;
		PreviewParameters params = pageParameters.getParams();
		if (params.getReportName().isEmpty() && params.getReportId()!=null && !params.getReportId().isEmpty()) {
			LambdaQueryWrapper<ReportFiles> wrapper = new LambdaQueryWrapper<>();
			wrapper.eq(ReportFiles::getId, params.getReportId());
			ReportFiles reportFiles = reportFilesMapper.selectOne(wrapper);
			params.setReportName(reportFiles.getFileName());
		}
		ReportDefinition reportDefinition = getReportDefinition(request, params);
		Map<String, Object> parameters = params.getQuery();
		Report report = reportRender.render(reportDefinition, parameters);
		List<Cell> lazyComputeCells = report.getLazyComputeCells();
		log.info("ReportPreviewAction#checkReportIsWarmLimit校验的单元格个数为：{}", lazyComputeCells.size());
		isLimit = lazyComputeCells.stream().filter(m -> m.isOverLimit()).collect(Collectors.toList()).size() > 0;
		isWarm = lazyComputeCells.stream().filter(m -> m.isOverWarning()).collect(Collectors.toList()).size() > 0;
		log.info("ReportPreviewAction#checkReportIsWarmLimit校验结果为 超限：{},超预警：{}", isLimit,isWarm);
		HashMap<String, Boolean> map = new HashMap<>();
		map.put("limit", isLimit);
		map.put("warm", isWarm);
		return new Result(map);
	}

	/**
	 * 保存数据到达标识的配置
	 * @return
	 */
	@PostMapping("saveDataArrivedCfg")
	public Result saveDataArrivedCfg (@RequestBody List<ReportCheckSource> reportCheckSources) {
		log.info("保存报表id为：{}的数据到达标识的配置", reportCheckSources.stream().map(ReportCheckSource::getReportId).collect(Collectors.toList()));
		String reportId = reportCheckSources.stream().map(ReportCheckSource::getReportId).collect(Collectors.toList()).get(0);
		// 先删后插
		reportCheckSourceMapper.delete(new LambdaQueryWrapper<ReportCheckSource>().eq(ReportCheckSource::getReportId,
				reportId));
		reportCheckSourceService.saveBatch(reportCheckSources);
		return new Result("保存成功", "成功");
	}

	/**
	 * 根据报表id查询此张报表的所有数据到达标识配置
	 * @param reportId
	 * @return
	 */
	@GetMapping("queryReportDataArrivedCfg")
	public Result queryReportDataArrivedCfg ( @PathVariable("reportId") String reportId) {
		List<ReportCheckSource> list = reportCheckSourceService.list(new LambdaQueryWrapper<ReportCheckSource>().eq(ReportCheckSource::getReportId, reportId));
		return new Result(list);
	}

	@RequestMapping("/refreshForm")
	public Map<String, Object> previewRefreshForm(HttpServletRequest request, @RequestBody PreviewSearchForm form) {
		ReportDefinition reportDefinition = getReportDefinition(request, form);
		return reportDefinition.refreshSearchForm(form.getItem(), form.getQuery());
	}

	@RequestMapping("/print")
	public void buildPrint(HttpServletRequest request, @RequestBody PreviewParameters reportParameters,HttpServletResponse response) {
		long start = System.currentTimeMillis();
		ReportDefinition reportDefinition = getReportDefinition(request, reportParameters);
		Report report = reportRender.render(reportDefinition, reportParameters.getQuery());
		Map<String, String> chartImages = reportParameters.getChartImages();
		report.setChartImages(chartImages);
		try {
			OutputStream out = response.getOutputStream();
			response.setCharacterEncoding("UTF-8");
			response.setContentType("application/pdf; charset=UTF-8");
			// 创建下载文件
			ProducerEnum p = ProducerEnum.PDF;
			String downFileName = StringUtils.randomFileName() + StringUtils.getFileSuffix(p);
			response.setHeader("Content-Disposition","attachment; filename=" + URLEncoder.encode(downFileName, "UTF-8"));
			response.setHeader("code", "20000");
			ExportUtils.export(out, report, p);
			long end = System.currentTimeMillis();
			Utils.logToConsole("获取打印文件耗时:" + (end - start) + "ms");
		} catch (Exception ex) {
			throw new ReportException(ex);
		}
	}

	@RequestMapping("/download/{type}")
	public void download(HttpServletRequest request, @PathVariable String type, @RequestBody PreviewParameters reportParameters, HttpServletResponse response) throws IOException {
		log.info("ReportPreviewAction#download方法下载报表开始，请求类型：{},请求参数：{}", type, reportParameters);
		ReportDefinition reportDefinition = getReportDefinition(request, reportParameters);
		String minDate = reportDefinition.getMinDate();
		Map<String, Object> query = reportParameters.getQuery();
		Report report = reportRender.render(reportDefinition, reportParameters.getQuery());
		Map<String, String> chartImages = reportParameters.getChartImages();
		report.setChartImages(chartImages);
//		String file = report.getReportFullName();
		String reportId = reportParameters.getReportId();
		OutputStream out = null;
		Paper paper = reportDefinition.getPaper();
		try {
			out = response.getOutputStream();
			response.setCharacterEncoding("UTF-8");
			response.setContentType("application/octet-stream; charset=UTF-8");
			// 创建下载文件
			ProducerEnum p = ProducerEnum.valueOf(type.toUpperCase());
//			String downFileName = StringUtils.randomFileName() + StringUtils.getFileSuffix(p);
			String reportName = reportFilesMapper.getReportName(reportParameters.getReportName());
			String excelName = (StringUtils.isNotBlank(reportName) ? reportName   : "未命名") + StringUtils.getFileSuffix(p);
			if (minDate==null || "".equals(minDate)){
				minDate = StringUtils.getTodayDateFormatter(LocalDateTime.now());
			}
			String downFileName = "【" +minDate + "】" + (StringUtils.isNotBlank(reportName) ? reportName   : "未命名") + StringUtils.getFileSuffix(p);
//			excelName = "【" +minDate + "】" + excelName;
			response.setHeader("Content-Disposition","attachment; filename=" + URLEncoder.encode(downFileName, "UTF-8"));
			response.setHeader("code", "20000");
			long start = System.currentTimeMillis();
//			Map<String, Object> parameters = buildParameters(request);
			if(type.equals("sheetPadding") || (paper.isPageEnabled() && "excel".equals(type)) ){
				log.info("ReportPreviewAction#download下载excel类型报表开始...");
				LambdaQueryWrapper<ReportFiles> wrapper = new LambdaQueryWrapper<>();
				wrapper.eq(ReportFiles::getId,reportId);
				ReportFiles reportFiles = reportFilesMapper.selectOne(wrapper);
				List<String> sheetNames = null;
				if (reportFiles!=null){
					String names = reportFiles.getSheetNames();
					if (names!=null){
						sheetNames = Arrays.asList(names.split(","));
					}
					report.setSheetNames(sheetNames);
				}
				for (Map.Entry<String, Object> entry : query.entrySet()){
					if ("T-1".equals(entry.getValue())){
						query.put(entry.getKey(), operationLogMapper.getBusiDate(LocalDateTime.now().toString()));
					}
					if ("T-2".equals(entry.getValue())){
						query.put(entry.getKey(), operationLogMapper.getYesterDate(LocalDateTime.now().toString()));
					}
					if ("当天".equals(entry.getValue())){
						query.put(entry.getKey(), com.bstek.common.utils.StringUtils.getTodayDateFormatter(LocalDateTime.now()));
					}
				}
				exportManager.exportExcelWithPagingSheet(new ExportConfigureImpl(reportParameters.getReportName(),query, out,sheetNames));
				log.info("ReportPreviewAction#download下载excel类型报表结束...");
			}else {
				ExportUtils.export(out, report, p);
			}
			long end = System.currentTimeMillis();
			Utils.logToConsole("导出 " + type + " 报表耗时:" + (end - start) + "ms");
			log.info("ReportPreviewAction#download方法下载报表结束，请求类型：{},导出耗时：{}", type, (end - start) + "ms");
			// 刷新输出流
			out.flush();
		} catch (Exception ex) {
			log.error("ReportPreviewAction#download方法下载报表异常，请求类型：{}", type, ex);
			throw new ReportException(ex);
		}finally {
			if (out != null) {
				out.close();
			}
		}
	}

	@RequestMapping("/pdf/{reportName}")
	public void buildPdf(HttpServletRequest request, HttpServletResponse response, @PathVariable String reportName)  throws IOException{
		PreviewParameters reportParameters = buildPreviewParameters(request);
		reportParameters.setReportName(reportName);
		download(request, "PDF", reportParameters,response);
	}

	@RequestMapping("/word/{reportName}")
	public void buildWord(HttpServletRequest request, HttpServletResponse response, @PathVariable String reportName)  throws IOException{
		PreviewParameters reportParameters = buildPreviewParameters(request);
		reportParameters.setReportName(reportName);
		download(request, "WORD", reportParameters,response);
	}

	@RequestMapping("/excel/{reportName}")
	public void buildExcel(HttpServletRequest request, HttpServletResponse response, @PathVariable String reportName) throws IOException {
		PreviewParameters reportParameters = buildPreviewParameters(request);
		reportParameters.setReportName(reportName);
		download(request, "EXCEL", reportParameters,response);
	}

	private ReportDefinition getReportDefinition(HttpServletRequest request,PreviewParameters params) {
		ReportDefinition reportDefinition = null;
		String reportName = params.getReportName();
		String content = params.getContent();
		List<String> minDate = new ArrayList<>();

		if (StringUtils.isNotBlank(reportName)) {
			reportDefinition = reportRender.getReportDefinition("file:" + reportName);
		} else if (StringUtils.isNotBlank(content)) {
			content = StringUtils.decode(content);
			reportDefinition = reportRender.getReportDefinition(content, "utf-8");
		} else {
			throw new ReportComputeException("Report file can not be null.");
		}
		if (reportDefinition == null) {
			throw new ReportDesignException("Report data has expired,can not do preview.");
		}
		Map<String, Object> parameters = params.getQuery();
		if(parameters == null) {
			parameters = new HashMap<String, Object>();
			params.setQuery(parameters);
		}
		Map<String, String> component = params.getComponent();
		if(component == null) {
			component = new HashMap<String, String>();
			params.setComponent(component);
		}
		// 相同的低优先级的参数会被高优先级参数覆盖
		// 参数优先级， token参数 > 页面表单参数 > url链接参数 > 数据集默认参数
		SearchForm searchForm = reportDefinition.getSearchForm();
		Map<String,String> map = new HashMap<>();
		if (searchForm!=null){
			List<Component> components = searchForm.getComponents();
			for (Component compEntry : components){
				if (compEntry instanceof DateInputComponent){
					DateInputComponent dateInputComponent = (DateInputComponent) compEntry;
					map.put(dateInputComponent.getBindParameter(),dateInputComponent.getFormat());
				}
			}
		}
		List<DatasourceDefinition> datasources = reportDefinition.getDatasources();
		for (DatasourceDefinition datasource : datasources){
			if (datasource instanceof BuildinDatasourceDefinition) {
				BuildinDatasourceDefinition buildinDatasource = (BuildinDatasourceDefinition) datasource;
				List<DatasetDefinition> datasets = buildinDatasource.getDatasets();
				for (DatasetDefinition dataset : datasets){
					if (dataset instanceof SqlDatasetDefinition){
						SqlDatasetDefinition sqlDataset = (SqlDatasetDefinition) dataset;
						for (Parameter parameter : sqlDataset.getParameters()){
							String formate = parameter.getName();
							String busiDate = operationLogAction.getBusiDate();
							String yesterday = operationLogAction.getYesterDate();
							String todayDate= StringUtils.getTodayDateFormatter(LocalDateTime.now());
							if ("T-1".equals(parameter.getDefaultValue())) {
								parameter.setDefaultValue(busiDate);
								minDate.add(busiDate);
								if ("date".equals(map.get(formate))){
									parameter.setDefaultValue(StringUtils.convertDateFormat(busiDate));
								}
							}
							else if ("T-2".equals(parameter.getDefaultValue())) {
								parameter.setDefaultValue(yesterday);
								minDate.add(yesterday);
								if ("date".equals(map.get(formate))){
									parameter.setDefaultValue(StringUtils.convertDateFormat(yesterday));
								}
							}else if ("当天".equals(parameter.getDefaultValue())){
								parameter.setDefaultValue(todayDate);
								minDate.add(todayDate);
								if ("date".equals(map.get(formate))){
									parameter.setDefaultValue(StringUtils.convertDateFormat(todayDate));
								}
							}
							if (parameter.getDefaultValue()!=null && !parameter.getDefaultValue().isEmpty()){
								if (DataType.Date.equals(parameter.getType())){
									minDate.add(parameter.getDefaultValue());
								}
							}
						}
					}
				}
			}
		}


		String token = request.getHeader(TokenFilter.X_TOKEN);
		if(org.apache.commons.lang3.StringUtils.isBlank(token)) {
			Cookie[] cookies = request.getCookies();
			if(cookies != null && cookies.length > 0) {
				for (Cookie cookie : cookies) {
					String name = cookie.getName();
					if(TokenFilter.X_TOKEN.equals(name)) {
						token = cookie.getValue();
						break;
					}
				}
			}
		}
		parameters.putAll(JWTUtil.getParams(token));
		reportDefinition.setToken(token);
		reportDefinition.setReportId(params.getReportId());
		List<Map<String, String>> searchFromData = params.getSearchFromData();
		if (searchFromData != null) {
			minDate.clear();
			minDate = searchFromData.stream()
					.filter(map1 -> "date".equals(map1.get("type"))) // 过滤出 type 为 "date" 的 map
					.map(map1 -> map1.get("value")) // 提取 value 字段
					.filter(Objects::nonNull) // 过滤掉 value 为 null 的情况
					.map(value -> value.replace("-", "")) // 去除 value 中的 "-"
					.collect(Collectors.toList()); // 收集结果到 minDate 列表
		}
		Optional<String> dateList = findMinDate(minDate);
		if (dateList.isPresent()) {
			reportDefinition.setMinDate(dateList.get());
			log.info("最小的日期是: " + dateList.get());
		} else {
			reportDefinition.setMinDate(StringUtils.getTodayDateFormatter(LocalDateTime.now()));
			log.info("没有有效的日期字符串。");
		}
		return reportDefinition;
	}


	public static Optional<String> findMinDate(List<String> dateStrings) {
		if (dateStrings == null || dateStrings.isEmpty()) {
			return Optional.empty();
		}

		DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("yyyyMMdd");
		DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyyMMdd");
		LocalDate minDate = null;

		for (String dateString : dateStrings) {
			if (dateString == null || dateString.trim().isEmpty()) {
				System.out.println("跳过空字符串: " + dateString);
				continue;
			}

			try {
				LocalDate date = LocalDate.parse(dateString, inputFormatter);
				if (minDate == null || date.isBefore(minDate)) {
					minDate = date;
				}
			} catch (Exception e) {
				// 忽略无效的日期字符串
				System.out.println("无效的日期字符串: " + dateString);
			}
		}

		if (minDate != null) {
			return Optional.of(minDate.format(outputFormatter));
		} else {
			return Optional.empty();
		}
	}


	private PreviewParameters buildPreviewParameters(HttpServletRequest req){
		PreviewParameters previewParameters = new PreviewParameters();
		Map<String, Object> parameters = previewParameters.getQuery();
		if(parameters == null) {
			parameters = new HashMap<String, Object>();
			previewParameters.setQuery(parameters);
		}
		Enumeration<?> enumeration=req.getParameterNames();
		while(enumeration.hasMoreElements()){
			Object obj = enumeration.nextElement();
			if(obj == null){
				continue;
			}
			String name = String.valueOf(obj);
			parameters.put(name, req.getParameter(name));
		}
		return previewParameters;
	}

	public Map<String, Object> createParam(ReportDefinition reportDefinition, Map<String, Object> parameters){
		if (parameters.get("userId") != null && StringUtils.isNotBlank(parameters.get("userId").toString())) {
			List<ReportDataPermission> reportDataPermissions = reportDataPermissionService.
					queryReportDataPermission(reportDefinition.getReportId());
			log.info("####====buildDatasetMap###方法参数reportDataPermissions大小：{}", reportDataPermissions.size());
			String userId = parameters.get("userId").toString();
			List<String> orgIds = reportDataPermissionMapper.queryUserOrgPermission(userId);
			//判断是否是系统管理员角色、系统管理员角色拥有所有数据权限
			List<String> sysUsers = reportDataPermissionMapper.queryAllSystemPermission();
			if (sysUsers.contains(userId)){
				log.info("当前用户ID:{}为系统管理员无需权限控制", userId);
				return parameters;
			}
			log.info("用户：{}，部门：{}，报表 ID：{}", userId, orgIds,  reportDefinition.getReportId());
			if (reportDataPermissions.size() > 0) {
				parameters.put("orgIds", orgIds);
				parameters.put("dataPermissionConf", reportDataPermissions);
			}
		}
		return parameters;
	}

}
