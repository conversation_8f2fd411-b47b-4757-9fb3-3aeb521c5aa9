package com.bstek.designer.bean;

public class PreviewPageParameters {

	private int pageIndex;
	
	private PreviewParameters params;

	private String reportId;


	public String getReportId() {
		return reportId;
	}

	public void setReportId(String reportId) {
		this.reportId = reportId;
	}

	public int getPageIndex() {
		return pageIndex;
	}

	public void setPageIndex(int pageIndex) {
		this.pageIndex = pageIndex;
	}

	public PreviewParameters getParams() {
		return params;
	}

	public void setParams(PreviewParameters params) {
		this.params = params;
	}

	@Override
	public String toString() {
		final StringBuffer sb = new StringBuffer("PreviewPageParameters{");
		sb.append("pageIndex=").append(pageIndex);
		sb.append(", reportId='").append(reportId).append('\'');
		sb.append('}');
		return sb.toString();
	}
}
