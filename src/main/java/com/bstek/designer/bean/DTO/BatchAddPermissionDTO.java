package com.bstek.designer.bean.DTO;

import java.util.List;

/**
 * 批量添加报表权限的请求体对象
 */
public  class BatchAddPermissionDTO {
    private String reportId;
    private List<String> userIds;
    private String permissionType;

    public String getReportId() {
        return reportId;
    }

    public void setReportId(String reportId) {
        this.reportId = reportId;
    }

    public List<String> getUserIds() {
        return userIds;
    }

    public void setUserIds(List<String> userIds) {
        this.userIds = userIds;
    }

    public String getPermissionType() {
        return permissionType;
    }

    public void setPermissionType(String permissionType) {
        this.permissionType = permissionType;
    }
}