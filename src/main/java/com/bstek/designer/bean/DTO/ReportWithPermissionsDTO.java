package com.bstek.designer.bean.DTO;

import com.bstek.designer.bean.PO.ReportFiles;
import com.bstek.system.bean.po.PfUser;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 报表权限DTO，包含报表信息和用户权限列表
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ReportWithPermissionsDTO {
    /**
     * 报表信息
     */
    private ReportFiles reportFiles;
    
    /**
     * 用户权限列表
     */
    private List<UserPermissionDTO> userPermissions;
    
    /**
     * 用户权限信息DTO
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class UserPermissionDTO {
        /**
         * 用户信息
         */
        private PfUser user;
        
        /**
         * 权限类型
         */
        private String permissionType;
    }
}