package com.bstek.designer.bean.PO;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date 2025/1/8 15:13
 * @desc 报表与数据集中数据到达标识的映射表
 */
@TableName("r_files")
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class ReportCheckSource {

    @TableId
    private int id;

    @TableField(value = "REPORT_ID")
    private String reportId;

    @TableField(value = "DATA_SOURCE")
    private String dataSource;

    @TableField(value = "sql")
    private String sql;

    @TableField(value = "param")
    private String param;

    @TableField(value = "UPDATE_TIME")
    private LocalDateTime updateTime;

    @TableField(value = "create_time")
    private LocalDateTime createTime;

    @TableField(value = "created_by")
    private String created_by;

    @TableField(value = "updated_by")
    private String updated_by;

}
