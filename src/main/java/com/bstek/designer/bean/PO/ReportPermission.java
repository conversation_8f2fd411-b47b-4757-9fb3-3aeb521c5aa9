package com.bstek.designer.bean.PO;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

/**
 * 报表权限实体类
 */
@TableName("r_report_permission")
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class ReportPermission {

    @TableId
    @TableField(value = "id")
    private String id;

    @TableField(value = "report_id")
    private String reportId;

    @TableField(value = "user_id")
    private String userId;

    @TableField(value = "permission_type")
    private String permissionType;

    @TableField(value = "create_by")
    private String createBy;

    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "create_date")
    private Date createDate;

    @TableField(value = "update_by")
    private String updateBy;

    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "update_date")
    private Date updateDate;
}