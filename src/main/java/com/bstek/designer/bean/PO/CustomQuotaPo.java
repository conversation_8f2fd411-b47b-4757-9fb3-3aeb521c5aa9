package com.bstek.designer.bean.PO;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class CustomQuotaPo {

    private String positionName;
    private String dimensionValue1;
    private String departmentName;
    private String desk1Name;
    private String desk2Name;
    private String quotaNameCn;
    private String quotaNameEn;
    private String status;
    private Double quotaValue;
    private Double presentValue;
    private String industry1;
    private String threshold;
    private String earlyWarningLine;
}


