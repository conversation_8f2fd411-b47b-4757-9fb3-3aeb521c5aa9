package com.bstek.designer.bean.PO;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2024/6/25 16:05
 */
@TableName("r_files")
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class ReportFiles {

    @TableId
    @TableField(value = "id")
    private String id;

    @TableField(value = "report_name")
    private String reportName;

    @TableField(value = "file_name")
    private String fileName;

    @TableField(exist = false)
    private String groupType;
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "create_date")
    private Date createDate;

    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "update_date")
    private Date updateDate;

@TableField(value = "publish_menu_name")
    private String publishMenuName;

@TableField(value = "content")
    private String content;

    @TableField(value = "parent_id")
    private String parentId;

    @TableField(value = "menu_id")
    private String menuId;

    @TableField(value = "sheet_names")
    private String sheetNames;

    @TableField(exist = false)
    private boolean isPublish;

    @TableField(exist = false)
    private String name;
    @TableField(exist = false)
    private String oldName;

    @TableField(value = "update_by")
    private String updateBy;


    @TableField(value = "create_by")
    private String createBy;

}
