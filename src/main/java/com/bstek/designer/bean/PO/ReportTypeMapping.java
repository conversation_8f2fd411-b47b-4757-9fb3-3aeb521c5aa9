package com.bstek.designer.bean.PO;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 报表和类型映射表- 一张报表可能有多个类型
 * <AUTHOR>
 * @Date 2024/6/27 9:24
 */
@TableName("r_file_type_mapping")
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class ReportTypeMapping {
    @TableId
    private String id;

    private String reportId;

    private String groupId;
}
