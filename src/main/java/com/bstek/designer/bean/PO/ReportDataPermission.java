package com.bstek.designer.bean.PO;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 灵活报表配置的数据集以及其中的部门字段
 * <AUTHOR>
 * @Date 2025/2/21 13:27
 */
@Data
@TableName("report_data_permission")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ReportDataPermission {

    /**
     * ID
     */
    @TableField("id")
    private String id;

    /**
     * 报表ID
     */
    @TableField("report_id")
    private String reportId;

    /**
     * 报表名称
     */
    @TableField("report_name")
    private String reportName;

    /**
     * 数据源的名称
     */
    @TableField("datasource_name")
    private String datasourceName;

    /**
     * 数据集的名称
     */
    @TableField("dataset")
    private String dataset;

    /**
     * 部门字段
     */
    @TableField("dataset_col")
    private String datasetCol;

    /**
     * 创建日期
     */
    @TableField("created_time")
    private Date createdTime;

    /**
     * 创建人
     */
    @TableField("created_by")
    private String createdBy;

    /**
     * 修改日期
     */
    @TableField("updated_time")
    private Date updatedTime;

    /**
     * 修改人
     */
    @TableField("updated_by")
    private String updatedBy;
}
