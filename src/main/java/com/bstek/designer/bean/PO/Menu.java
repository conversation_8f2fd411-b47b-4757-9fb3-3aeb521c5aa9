package com.bstek.designer.bean.PO;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.beans.Transient;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/6/24 10:56
 */
@TableName("pf_menu")
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class Menu {
    private String id;
    private String name;
    private String orders;
    private String icon;
    private String leafFlag;
    private String tip;
    private String type;
    private String issamewin;
    private String parentId;
    private String url;
    private String img;
    /***
     * 是否启用，0-停用；1-启用
     */
    private String isEnabled;
    private String remarks;
    private Date createTime;
    private Date updateTime;
    private String createBy;
    private String updateBy;

    private String isUreport;


    @TableField(exist = false)
    private List<Menu> submenu= new ArrayList<Menu>();
}
