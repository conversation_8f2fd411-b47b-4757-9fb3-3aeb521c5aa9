package com.bstek.designer.bean.PO;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;


@TableName("REPORT_CONF")
@AllArgsConstructor
@NoArgsConstructor
@Setter
@Getter
public class ReportConfPo {
    @TableId
    private String id;

    @TableField(value = "CONTENT")
    private String content;

    private String reportDate;

    private String reportModelName;

    private String createTime;

    private String updateTime;
}
