package com.bstek.designer.service;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @create 2025/01/14
 **/
public interface IMailService   {

    /**
     * 发送文本邮件
     *
     * @param to      收件人
     * @param subject 主题
     * @param content 内容
     */
    void sendSimpleMail(String to, String subject, String content);

    /**
     * 发送HTML邮件
     *
     * @param to      收件人
     * @param subject 主题
     * @param content 内容
     */
    void sendHtmlMail(String to, String subject, String content);


    /**
     * 发送HTML邮件
     *
     * @param to      多个收件人
     * @param subject 主题
     * @param content 内容
     */
    void sendHtmlMail(List<String> to, String subject, String content);

    /**
     * 发送带附件的邮件
     *
     * @param to       收件人
     * @param subject  主题
     * @param content  内容
     * @param filePaths 附件
     */
    public void sendAttachmentsMail(String to, String subject, String content, List<String> filePaths) ;

    /**
     * 发送模板邮件
     * @param to 收件人
     * @param subject 主题
     * @param fileName 邮件模板文件名称
     * @param model 邮件数据载体
     */
    void sendModelMail(String to, String subject, String fileName, Object model);


    /**
     * 发送带附件的HTML邮件
     *
     * @param to       收件人
     * @param subject  主题
     * @param content  HTML内容
     * @param filePaths 附件路径列表
     */
    public void sendHtmlMailWithAttachments(String to, String subject, String content, List<String> filePaths,List<String> attachNames);

}