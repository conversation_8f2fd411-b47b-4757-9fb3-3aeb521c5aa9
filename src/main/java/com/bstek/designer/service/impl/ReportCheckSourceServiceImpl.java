package com.bstek.designer.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bstek.designer.bean.PO.ReportCheckSource;
import com.bstek.designer.mapper.ReportCheckSourceMapper;
import com.bstek.designer.service.ReportCheckSourceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Date 2025/1/10 9:40
 */
@Service
@Slf4j
public class ReportCheckSourceServiceImpl extends ServiceImpl<ReportCheckSourceMapper, ReportCheckSource>  implements ReportCheckSourceService {

    @Autowired
    private ReportCheckSourceMapper reportCheckSourceMapper;



}
