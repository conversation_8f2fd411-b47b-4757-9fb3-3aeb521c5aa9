package com.bstek.designer.service.impl;

import com.bstek.designer.service.IMailService;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.FileSystemResource;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.mail.MessagingException;
import javax.mail.internet.MimeMessage;
import javax.mail.internet.MimeUtility;
import java.io.File;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description
 * @create 2025/01/14
 **/
@Slf4j
@Service
@AllArgsConstructor
@NoArgsConstructor
public class IMailServiceImpl implements IMailService {

    /**
     * Spring Boot 提供了一个发送邮件的简单抽象，使用的是下面这个接口，这里直接注入即可使用
     */
    @Resource
    private JavaMailSender mailSender;

    /**
     * 发送人
     */
    @Value("${spring.mail.from}")
    private String from;

    /**
     * 发件人名称
     */
    @Value("${spring.mail.personal}")
    private String personal;

    /**
     * 简单文本邮件
     *
     * @param to      收件人
     * @param subject 主题
     * @param content 内容
     */
    @Override
    @Async("mailExecutor")
    public void sendSimpleMail(String to, String subject, String content) {
        //创建SimpleMailMessage对象
        SimpleMailMessage message = new SimpleMailMessage();
        //邮件发送人
        message.setFrom(from);
        //邮件接收人
        message.setTo(to);
        //邮件主题
        message.setSubject(subject);
        //邮件内容
        message.setText(content);
        //发送邮件
        mailSender.send(message);
    }

    /**
     * html邮件
     *
     * @param to      收件人
     * @param subject 主题
     * @param content 内容
     */
    @Override
    @Async("mailExecutor")
    public void sendHtmlMail(String to, String subject, String content) {
        //获取MimeMessage对象
        MimeMessage message = mailSender.createMimeMessage();
        MimeMessageHelper messageHelper;
        try {
            messageHelper = new MimeMessageHelper(message, true);
            //邮件发送人
            messageHelper.setFrom(from, personal);
            //邮件接收人
            messageHelper.setTo(to);
            //邮件主题
            message.setSubject(subject);
            //邮件内容，html格式
            messageHelper.setText(content, true);
            //发送
            mailSender.send(message);
            //日志信息
            log.info("邮件已经发送...");
        } catch (MessagingException e) {
            log.error("发送邮件时发生异常！", e);
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * html邮件
     *
     * @param to      多个收件人
     * @param subject 主题
     * @param content 内容
     */
    @Override
    @Async("mailExecutor")
    public void sendHtmlMail(List<String> to, String subject, String content) {
        // 获取MimeMessage对象
        MimeMessage message = mailSender.createMimeMessage();
        MimeMessageHelper messageHelper;
        try {
            messageHelper = new MimeMessageHelper(message, true, "UTF-8");
            // 设置发件人
            messageHelper.setFrom(from, personal);
            log.info("设置发件人: {}", from);

            // 设置收件人列表
            messageHelper.setTo(to.toArray(new String[0]));
            log.info("设置收件人: {}", to);

            // 设置邮件主题
            messageHelper.setSubject(subject);
            log.info("设置邮件主题: {}", subject);

            // 设置邮件内容，HTML格式
            messageHelper.setText(content, true);
            log.info("设置邮件内容: {}", content);

            // 发送邮件
            mailSender.send(message);
            log.info("邮件已成功发送给: {}", to);
        } catch (MessagingException e) {
            log.error("发送邮件时发生异常！", e);
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }
    }


    /**
     * 带附件的邮件
     * @param to       收件人
     * @param subject  主题
     * @param content  内容
     * @param filePaths 附件
     */
    @Override
    @Async("mailExecutor")
    public void sendAttachmentsMail(String to, String subject, String content, List<String> filePaths) {
        MimeMessage message = mailSender.createMimeMessage();
        try {
            MimeMessageHelper helper = new MimeMessageHelper(message, true);
            helper.setFrom(from, personal);
            helper.setTo(to);
            helper.setSubject(subject);
            helper.setText(content, true);

            // 添加附件
            for (String filePath : filePaths) {
                ClassPathResource resource = new ClassPathResource(filePath);
                FileSystemResource file = new FileSystemResource(resource.getFile());
                helper.addAttachment(Objects.requireNonNull(file.getFilename()), file);
            }

            mailSender.send(message);
            // 日志信息
            log.info("邮件已经发送...");
        } catch (MessagingException | IOException e) {
            log.error("发送邮件时发生异常！", e);
        }
    }


    /**
     * 发送带附件的HTML邮件
     *
     * @param to       收件人
     * @param subject  主题
     * @param content  HTML内容
     * @param filePaths 附件路径列表
     */
    @Override
    @Async("mailExecutor")
    public void sendHtmlMailWithAttachments(String to, String subject, String content, List<String> filePaths, List<String> attachNames) {
        MimeMessage message = mailSender.createMimeMessage();
        try {
            MimeMessageHelper helper = new MimeMessageHelper(message, true);

            // 设置发件人
            helper.setFrom(from, "风险管理工作平台");
            log.info("设置发件人: {}", from);

            // 设置收件人
            helper.setTo(to);
            log.info("设置收件人: {}", to);

            // 设置邮件主题
            helper.setSubject(subject);
            log.info("设置邮件主题: {}", subject);

            // 设置邮件内容，HTML格式
            helper.setText(content, true);
            log.info("设置邮件内容: {}", content);

            // 添加附件
            if (filePaths != null && !filePaths.isEmpty()) {
                log.info("开始添加附件...");
                for (int i = 0; i < filePaths.size(); i++) {
                    helper.addAttachment(MimeUtility.encodeText(attachNames.get(i)), new File(filePaths.get(i)));
                    log.info("添加附件:{}成功", attachNames.get(i));
                }
            } else {
                log.info("没有附件需要添加");
            }

            // 发送邮件
            mailSender.send(message);
            log.info("邮件已成功发送给: {}", to);
        } catch (Exception e) {
            log.info("邮件发送异常！！！！",e);
            log.error("发送邮件时发生异常！", e);
        }
    }


    @Override
    @Async("mailExecutor")
    public void sendModelMail(String to, String subject, String fileName, Object model) {
    }
}