package com.bstek.designer.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bstek.designer.bean.PO.ReportDataPermission;
import com.bstek.designer.mapper.ReportDataPermissionMapper;
import com.bstek.designer.service.ReportDataPermissionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/2/21 15:13
 */
@Service
@Slf4j
public class ReportDataPermissionServiceImpl extends ServiceImpl<ReportDataPermissionMapper, ReportDataPermission>
        implements ReportDataPermissionService {

    @Autowired
    private ReportDataPermissionMapper reportDataPermissionMapper;

    @Override
    public List<ReportDataPermission> queryReportDataPermission(String reportId) {
        List<ReportDataPermission> list = reportDataPermissionMapper.selectList
                (new LambdaQueryWrapper<ReportDataPermission>().eq(ReportDataPermission::getReportId, reportId));
        return list;
    }
}
