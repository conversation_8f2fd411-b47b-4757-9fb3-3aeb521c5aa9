package com.bstek.designer.Utils;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONObject;

import java.util.HashMap;
import java.util.Map;

/**
 * 调用平台接口工具类
 * <AUTHOR>
 * @Date 2024/6/25 9:48
 */
public class PlatFormUtils {

    /**
     * [{"userName":"admin","password":"0OBekc0tGJ/hLvwQPE9Z3w==","opMenu":""}]
     * 调用平台的登录接口，拿到对应的token，进行后续请求所使用
     * @param url
     * @return
     */
    public  static String login (String url,String paramsStr) {
        HttpRequest request = HttpUtil.createPost(url);
        Map<String,Object> formMap = new HashMap<>(4);
        formMap.put("className","LoginService");
        formMap.put("methodName","login");
        formMap.put("params",paramsStr);
        request.form(formMap);
        request.header("Content-Type","application/x-www-form-urlencoded");
        request.header("Accept","application/json, text/plain, */*");
        HttpResponse response = request.execute();
        String body = response.body();
        String token = JSONObject.parseObject(JSONObject.parseObject(body).get("data").toString(), Map.class).get("loginToken").toString();
        return token;
    }

    /**
     * 删除菜单数据 - 有通过调用接口的方式，才可以解决平台菜单数据缓存重刷问题
     * MenuManagerService
     * delMenu
     * [{"id":"Iff8080818f84580601904dffbbaf0042","opMenu":"系统管理/菜单管理"}]
     * @param url
     * @param paramsStr
     * @return
     */
    public static int delMenu (String url, String paramsStr,String token) {
        HttpRequest request = HttpUtil.createPost(url);
        Map<String,Object> formMap = new HashMap<>(4);
        formMap.put("className","MenuManagerService");
        formMap.put("methodName","delMenu");
        formMap.put("params",paramsStr);
        request.form(formMap);
        request.header("Content-Type","application/x-www-form-urlencoded");
        request.header("Accept","application/json, text/plain, */*");
        request.header("token",token);
        HttpResponse response = request.execute();
        String body = response.body();
        String success = JSONObject.parseObject(body).get("success").toString();
        return "true".equals(success) ? 1 : 0;
    }


    /**
     * class：MenuManagerService
     * method：saveOrUpdate
     * params； [{"id":"I2c9835c38703f2750187072dd3b00011","name":"日报复核1","img":"ProfileFilled","order":"3","parentId":"I2c9835c38703f2750187072a9a7e0005","pName":"经营&风险日报","type":"page","
     *  url":"/dailyManagement/dailyCheck","operationList":"",
     * 	   "operates":"[{\"functionId\":\"RECHECK\",\"functionName\":\"复核\",\"classMethod\":\"DataReviewService/valid;DataReviewService/invalid\"},{\"functionId\":\"QUERY\",\"functionName\":\"查询\",\"classMethod\":\"DataReviewService/queryPage\"}]",
     * 	   "opMenu":"系统管理/菜单管理"}]
     * 保存菜单数据 - 只有通过调用接口的方式，才可以解决平台菜单数据缓存重刷问题
     * @param url
     * @param paramsStr
     * @return
     */
    public static int saveMenu (String url,String paramsStr,String token) {
        HttpRequest request = HttpUtil.createPost(url);
        Map<String,Object> formMap = new HashMap<>(4);
        formMap.put("className","MenuManagerService");
        formMap.put("methodName","saveOrUpdate");
        formMap.put("params",paramsStr);
        request.form(formMap);
        request.header("Content-Type","application/x-www-form-urlencoded");
        request.header("Accept","application/json, text/plain, */*");
        request.header("token",token);
        HttpResponse response = request.execute();
        String body = response.body();
        String success = JSONObject.parseObject(body).get("success").toString();
        return "true".equals(success) ? 1 : 0;
    }



}
