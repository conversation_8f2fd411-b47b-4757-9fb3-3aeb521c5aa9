package com.bstek.designer.Utils;

import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.FileSystemResource;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.scheduling.annotation.Async;

import javax.mail.internet.MimeMessage;
import javax.mail.internet.MimeUtility;
import java.io.File;
import java.util.*;

/**
 * <AUTHOR>
 * @Date 2023/8/29 11:24
 */
public class EmailUtil {




    /**
     * 添加附件发送邮件
     * @param emailTO
     * @param emailFrom
     * @return
     */

    public String sendReportFileAsEmail(List<String> emailTO,
                                        String emailFrom,
                                        String attachName,
                                        String zipFile,
                                        String subject,
                                        String content,
                                        JavaMailSenderImpl mailSender) {
        MimeMessage mimeMessage = mailSender.createMimeMessage();

        try {
            MimeMessageHelper messageHelper = new MimeMessageHelper(mimeMessage,true);
            messageHelper.addAttachment(MimeUtility.encodeText(attachName),new File(zipFile));
            messageHelper.setSubject(subject);
            messageHelper.setFrom(emailFrom);
            messageHelper.setText(content,true);
            String[] emailsTo = emailTO.toArray(new String[emailTO.size()]);;
            messageHelper.setTo(emailsTo);
            mailSender.send(mimeMessage);
        }catch (Exception e ) {
            e.printStackTrace();
            return "发送邮件失败";
        }
        return "发送邮件成功";
    }


}
