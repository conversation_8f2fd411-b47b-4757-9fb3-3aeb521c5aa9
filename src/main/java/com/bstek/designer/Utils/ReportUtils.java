package com.bstek.designer.Utils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bstek.designer.bean.PO.ReportFiles;
import com.bstek.designer.bean.ReportDefinitionWrapper;
import com.bstek.designer.mapper.GroupInfoMapper;
import com.bstek.designer.mapper.ReportFilesMapper;
import com.bstek.system.action.OperationLogAction;
import com.bstek.ureport.definition.ReportDefinition;
import com.bstek.ureport.definition.dataset.DatasetDefinition;
import com.bstek.ureport.definition.dataset.Parameter;
import com.bstek.ureport.definition.dataset.SqlDatasetDefinition;
import com.bstek.ureport.definition.datasource.BuildinDatasourceDefinition;
import com.bstek.ureport.definition.datasource.DatasourceDefinition;
import com.bstek.ureport.export.ReportRender;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @create 2025/01/14
 **/
@Slf4j
@Component
public class ReportUtils {

    private ReportRender reportRender = new ReportRender();

    @Autowired
    private OperationLogAction operationLogAction;

    @Autowired
    private ReportFilesMapper reportFilesMapper;


    @Autowired
    private GroupInfoMapper groupInfoMapper;

    /**
     * 选取报表 查询参数信息
     *
     * @param file
     * @return
     */
    public Map<String, String> getDataSourceInfo(String file) {
        ReportDefinition reportDef = reportRender.parseReport(file);
        reportDef.setBusiDate(operationLogAction.getBusiDate());
        reportDef.setYesterDate(operationLogAction.getYesterDate());
//        String reportName = reportFilesMapper.getReportName(reportDef.getReportFullName().replace("file:", ""));
        String reportName = reportFilesMapper.getReportName(reportDef.getReportFullName().replace("s3:", ""));
        LambdaQueryWrapper<ReportFiles> wrapper = new LambdaQueryWrapper();
        wrapper.eq(ReportFiles::getReportName, reportName);
        ReportFiles reportFiles = reportFilesMapper.selectOne(wrapper);
        String reportId = null;
        if (reportFiles != null) {
            reportId = reportFiles.getId();
        }
        reportDef.setReportId(reportId);
        String groupName = groupInfoMapper.getGroupName(reportName);
        reportDef.setReportName(reportName);
        reportDef.setGroupName(groupName);
        ReportDefinitionWrapper definitionWrapper = new ReportDefinitionWrapper(reportDef);
        Map<String, String> parameterMap = new HashMap<>();
        for (DatasourceDefinition datasourceDefinition : definitionWrapper.getDatasources()) {
            BuildinDatasourceDefinition buildinDatasourceDefinition = (BuildinDatasourceDefinition) datasourceDefinition;
            for (DatasetDefinition dataset : buildinDatasourceDefinition.getDatasets()) {
                SqlDatasetDefinition sqlDatasetDefinition = (SqlDatasetDefinition) dataset;
                List<Parameter> parameters = sqlDatasetDefinition.getParameters();
                if (parameters != null) {
                    for (Parameter parameter : parameters) {
                        parameterMap.put(parameter.getName(), parameter.getDefaultValue());
                    }
                }
            }
        }

        log.info("获取报表参数信息：{}", parameterMap);
        return parameterMap;
    }
}
