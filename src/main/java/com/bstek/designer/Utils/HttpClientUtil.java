package com.bstek.designer.Utils;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.httpclient.DefaultHttpMethodRetryHandler;
import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.HttpException;
import org.apache.commons.httpclient.HttpStatus;
import org.apache.commons.httpclient.methods.GetMethod;
import org.apache.commons.httpclient.methods.PostMethod;
import org.apache.commons.httpclient.methods.StringRequestEntity;
import org.apache.commons.httpclient.params.HttpMethodParams;
import org.apache.commons.httpclient.protocol.Protocol;

import org.apache.commons.httpclient.protocol.SecureProtocolSocketFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.net.ssl.*;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLConnection;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;

/**
 * <AUTHOR>
 * @Date 2023/3/17 10:37
 */
@Slf4j
public class HttpClientUtil {

    private static Logger logger = LoggerFactory.getLogger(HttpClientUtil.class);


    /**
     * 覆盖java默认的证书验证
     */
    private static final TrustManager[] trustAllCerts = new TrustManager[]{new X509TrustManager() {
        public java.security.cert.X509Certificate[] getAcceptedIssuers() {
            return new java.security.cert.X509Certificate[]{};
        }

        public void checkClientTrusted(X509Certificate[] chain, String authType)
                throws CertificateException {
        }

        public void checkServerTrusted(X509Certificate[] chain, String authType)
                throws CertificateException {
        }
    }};


    /**
     * @param url
     * @param token
     * @return
     */
    public static byte[] doGet(String url, String token) {
        /**
         * 1.生成HttpClient对象并设置参数
         */
        HttpClient httpClient = new HttpClient();
        //设置Http连接超时为5秒
        httpClient.getHttpConnectionManager().getParams().setConnectionTimeout(5000);

        /**
         * 2.生成GetMethod对象并设置参数
         */
        GetMethod getMethod = new GetMethod(url);
        getMethod.addRequestHeader("X-Tableau-Auth", token);
        //设置get请求超时为5秒
        getMethod.getParams().setParameter(HttpMethodParams.SO_TIMEOUT, 5000);
        //设置请求重试处理，用的是默认的重试处理：请求三次
        getMethod.getParams().setParameter(HttpMethodParams.RETRY_HANDLER, new DefaultHttpMethodRetryHandler());

        String response = "";
        byte[] responseBody = null;
        /**
         * 3.执行HTTP GET 请求
         */
        try {
            int statusCode = httpClient.executeMethod(getMethod);

            /**
             * 4.判断访问的状态码
             */
            if (statusCode != HttpStatus.SC_OK) {
                System.err.println("请求出错：" + getMethod.getStatusLine());
            }

            /**
             * 5.处理HTTP响应内容
             */
            //读取HTTP响应内容，这里简单打印网页内容
            //读取为字节数组
            responseBody = getMethod.getResponseBody();
        } catch (HttpException e) {
            //发生致命的异常，可能是协议不对或者返回的内容有问题
            System.out.println("请检查输入的URL!");
            logger.warn("请检查输入的URL");
            e.printStackTrace();
        } catch (IOException e) {
            //发生网络异常
            System.out.println("发生网络异常!");
            logger.warn("发生网络异常");
        } finally {
            /**
             * 6.释放连接
             */
            getMethod.releaseConnection();
        }
        return responseBody;
    }


    /**
     * post请求
     *
     * @param url
     * @param json
     * @return
     */
//    public static byte[] doPost(String url, JSONObject json, String token) {
//        SecureProtocolSocketFactory factory = new SecureProtocolSocketFactory();
//        Protocol.registerProtocol("https", new Protocol("https", factory, 443));
//        HttpClient httpClient = new HttpClient();
//
//        PostMethod postMethod = new PostMethod(url);
//        postMethod.addRequestHeader("accept", "*/*");
//        postMethod.addRequestHeader("connection", "Keep-Alive");
//        //设置json格式传送
//        postMethod.addRequestHeader("Content-Type", "application/json;charset=utf-8");
//        //必须设置下面这个Header
//        postMethod.addRequestHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.81 Safari/537.36");
//        postMethod.addRequestHeader("X-Tableau-Auth", token);
//        //添加请求参数
//        //postMethod.addParameter("param", json.getString("param"));
//        StringRequestEntity param = new StringRequestEntity(json.toString());
//        postMethod.setRequestEntity(param);
//
//
//        String res = "";
//        byte[] responseBody = null;
//        try {
//            int code = httpClient.executeMethod(postMethod);
//            if (code == 200) {
//                responseBody = postMethod.getResponseBody();
//                res = new String(responseBody, "UTF-8");
//                //res = postMethod.getResponseBodyAsString();
//                return responseBody;
//            }
//        } catch (IOException e) {
//            e.printStackTrace();
//        }
//        return responseBody;
//    }

    /**
     * 信任所有
     *
     * @param connection
     * @return
     */
    private static SSLSocketFactory trustAllHosts(HttpsURLConnection connection) {
        SSLSocketFactory oldFactory = connection.getSSLSocketFactory();
        try {
            SSLContext sc = SSLContext.getInstance("SSL", "SunJSSE");
            sc.init(null, trustAllCerts, new java.security.SecureRandom());
            SSLSocketFactory newFactory = sc.getSocketFactory();
            connection.setSSLSocketFactory(newFactory);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return oldFactory;
    }

    /**
     * 设置不验证主机
     */
    private static final HostnameVerifier DO_NOT_VERIFY = new HostnameVerifier() {
        public boolean verify(String hostname, SSLSession session) {
            return true;
        }
    };

    public static void downloadReport(String urlstr, String filePath, String downloadToken, String param) throws IOException {
        // 文件下载的URL
        String fileUrl = urlstr;
        // 保存文件的路径
        String saveFilePath = filePath;
        // 获取文件的父目录
        Path path = Paths.get(saveFilePath).getParent();
        try {
            // 创建目录
            Files.createDirectories(path);
        } catch (Exception e) {
            e.printStackTrace();
            // 如果目录创建失败，则退出程序
            return;
        }
        try {
            URL url = new URL(fileUrl);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            // 设置请求方法为POST
            connection.setRequestMethod("POST");
            // 允许输出
            connection.setDoOutput(true);
            // 添加请求头
            connection.setRequestProperty("x-token", downloadToken);
            connection.setRequestProperty("Content-Type", "application/json;charset=UTF-8");
            connection.setRequestProperty("Userid", "admin");
            connection.setRequestProperty("Sunlinereport", "admin");
            // 发送POST请求
            OutputStream os = connection.getOutputStream();
            String requestParams = param;
            os.write(requestParams.getBytes(StandardCharsets.UTF_8));
            os.flush();
            os.close();

            // 检查响应状态
            int responseCode = connection.getResponseCode();
            if (responseCode == HttpURLConnection.HTTP_OK) {
                // 读取响应数据
                InputStream inputStream = connection.getInputStream();
                FileOutputStream outputStream = new FileOutputStream(saveFilePath);

                byte[] buffer = new byte[1024];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }

                // 关闭所有连接
                outputStream.close();
                inputStream.close();
                connection.disconnect();
                log.info("文件下载成功！");
            } else {
                log.error("文件下载失败. Response code: " + responseCode);
            }
        } catch (IOException e) {
            log.error("文件下载失败.");
            e.printStackTrace();
        }
    }

}
