package com.bstek.conditions.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bstek.conditions.bean.ureportConditions;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 条件属性配置
 * <AUTHOR>
 * @Date 2024/4/12 15:46
 */
@Mapper
public interface ureportConditionsMapper extends BaseMapper<ureportConditions> {
    /**
     * 查询所有的条件配置
     * @return
     */
    @Select("SELECT * FROM r_conditions")
    List<ureportConditions> queryAllConditions ();

}
