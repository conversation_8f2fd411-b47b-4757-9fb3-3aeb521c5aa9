package com.bstek.conditions.bean;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * ureport条件属性配置
 * <AUTHOR>
 * @Date 2024/4/12 15:41
 */
@TableName("r_conditions")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ureportConditions {
    private String cellStyle;
    private String name;
    private String conditions;
    @TableId
    private String id;
}
