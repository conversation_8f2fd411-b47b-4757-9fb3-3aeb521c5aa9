package com.bstek.conditions.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bstek.common.bean.Result;
import com.bstek.conditions.bean.Condition;
import com.bstek.conditions.bean.ureportConditions;
import com.bstek.conditions.mapper.ureportConditionsMapper;
import com.bstek.conditions.service.ureportConditionsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/4/12 15:50
 */
@Slf4j
@Service
public class ureportConditionsServiceImpl extends ServiceImpl<ureportConditionsMapper, ureportConditions> implements ureportConditionsService {

    @Autowired
    private ureportConditionsMapper  ureportConditionsMapper;

    /**
     * 查询所有的条件配置
     * @return
     */
    @Override
    public List<Map<String, Object>> queryAllConditions() {
        List<Map<String, Object>> resultMap = new ArrayList<>();
        List<ureportConditions> ureportConditions = ureportConditionsMapper.queryAllConditions();
        for (com.bstek.conditions.bean.ureportConditions ureportCondition : ureportConditions) {
            Map<String,Object> dataMap = new HashMap<>(4);
            dataMap.put("label",ureportCondition.getName());
            dataMap.put("value",ureportCondition.getId());
            dataMap.put("cellStyle","");
            if (StringUtils.isNotBlank(ureportCondition.getCellStyle())) {
                dataMap.put("cellStyle",JSONObject.parseObject(ureportCondition.getCellStyle(),new TypeReference<Map<String, Object>>() {
                }));
            }
            dataMap.put("conditions","");
            if (StringUtils.isNotBlank(ureportCondition.getConditions())) {
                dataMap.put("conditions", JSONObject.parseObject(ureportCondition.getConditions(), new TypeReference<List<Map<String, Object>>>() {
                }));
            }
            resultMap.add(dataMap);
        }
        return resultMap;
    }


    @Override
    public Result saveConditions(Condition condition) {
        String name = condition.getName();
        List<String> names = ureportConditionsMapper.queryAllConditions().stream().map(ureportConditions::getName).collect(Collectors.toList());
        if (names.contains(name)) {
            return new Result("保存失败，当前条件项名字与已有配置重复，请重新命名","fail");
        }
        String id = UUID.randomUUID().toString();
        String cellStyle = condition.getCellStyle() != null ? JSONObject.toJSONString(condition.getCellStyle()) : "";
        String conditionStr = condition.getConditions().isEmpty() ? "" : JSONObject.toJSONString(condition.getConditions());
        ureportConditions ureportConditions = new ureportConditions(cellStyle,name,conditionStr,id);
        try {
            ureportConditionsMapper.insert(ureportConditions);
            return new Result("success", id);
        }catch (Exception e) {
            log.error("保存失败，{}",e.getMessage());
            return new Result("fail","");
        }
    }
}
