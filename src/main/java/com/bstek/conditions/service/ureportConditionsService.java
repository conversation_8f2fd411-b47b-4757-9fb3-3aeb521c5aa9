package com.bstek.conditions.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bstek.common.bean.Result;
import com.bstek.conditions.bean.Condition;
import com.bstek.conditions.bean.ureportConditions;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2024/4/12 15:49
 */
public interface ureportConditionsService extends IService<ureportConditions> {
    List<Map<String,Object>> queryAllConditions ();

    /**
     * 保存条件配置入库
     * @param condition
     */
    Result saveConditions (Condition condition);
}
