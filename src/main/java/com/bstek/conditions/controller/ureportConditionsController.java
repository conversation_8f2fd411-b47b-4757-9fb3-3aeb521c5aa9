package com.bstek.conditions.controller;

import com.bstek.common.bean.Result;
import com.bstek.conditions.bean.Condition;
import com.bstek.conditions.service.ureportConditionsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 条件配置返回控制层
 * <AUTHOR>
 * @Date 2024/4/12 15:58
 */
@Slf4j
@RestController
@RequestMapping("/ureportConditions")
public class ureportConditionsController {

    @Resource
    private ureportConditionsService ureportConditionsService;


    @GetMapping("/queryConditions")
    public Result queryConditions () {
       return  new Result(ureportConditionsService.queryAllConditions());
    }


    /**
     * 保存条件配置入库实现复用
     * @param request
     * @return
     */
    @PostMapping("/saveConditon")
    public Result saveConditon (HttpServletRequest request, @RequestBody Condition condition) {
        return  ureportConditionsService.saveConditions(condition);
    }


}
