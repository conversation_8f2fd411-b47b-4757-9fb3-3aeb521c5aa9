package com.bstek.conditions.util;

import com.bstek.conditions.bean.ureportConditions;
import lombok.extern.slf4j.Slf4j;
import org.xml.sax.Attributes;
import org.xml.sax.ContentHandler;
import org.xml.sax.Locator;
import org.xml.sax.SAXException;

/**
 * 对保存的历史条件配置进行解析入库
 * <AUTHOR>
 * @Date 2024/4/12 16:09
 */
@Slf4j
public class ConditionParseUtil implements ContentHandler {

    // 当前标签
    private String currentTag;


    ureportConditions ureportConditions = new ureportConditions();


    public com.bstek.conditions.bean.ureportConditions getUreportConditions() {
        return ureportConditions;
    }

    @Override
    public void setDocumentLocator(Locator locator) {

    }

    @Override
    public void startDocument() throws SAXException {

    }

    @Override
    public void endDocument() throws SAXException {

    }

    @Override
    public void startPrefixMapping(String prefix, String uri) throws SAXException {

    }

    @Override
    public void endPrefixMapping(String prefix) throws SAXException {

    }

    @Override
    public void startElement(String uri, String localName, String qName, Attributes atts) throws SAXException {
        currentTag = qName;
        // 条件名称
        if ("condition-property-item".equals(currentTag)) {
            String name = atts.getValue("name");
            ureportConditions.setName(name);
            log.info("====== name =====:" + name);
        }

        if (currentTag.equals("cell-style")) {

        }


    }

    @Override
    public void endElement(String uri, String localName, String qName) throws SAXException {

    }

    @Override
    public void characters(char[] ch, int start, int length) throws SAXException {

    }

    @Override
    public void ignorableWhitespace(char[] ch, int start, int length) throws SAXException {

    }

    @Override
    public void processingInstruction(String target, String data) throws SAXException {

    }

    @Override
    public void skippedEntity(String name) throws SAXException {

    }
}
