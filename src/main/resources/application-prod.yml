ureport:
  support:
    datasource[0]:
      code: f0476b68-f880-2530-98d5-37bc83e2bcf9
      name: 平台impala数据源
      url: *******************************************************************=<EMAIL>;KrbHostFQDn=dpdcbd-dchd101;KrbServiceName=impala
      driverClassName: com.cloudera.impala.jdbc.Driver
      userName: DCFXGL_GW001
      password: Fxglpt@2022

    datasource[1]:
      code: be2a5284483b4edc9291dd2b84c3b5f3
      name: 市场风险数据库
      url: *******************************************
      driverClassName: oracle.jdbc.OracleDriver
      userName: etl
      password: etl5585354

    datasource[2]:
      code: b12a5284483b4edc9291dd2b84c3b5f3
      name: 风险自建库
      url: *******************************************
      driverClassName: oracle.jdbc.OracleDriver
      userName: onlyread
      password: onlyread

    datasource[3]:
      code: b22a5284483b4edc9291dd2b84c3b5f3
      name: 并表系统
      url: ***************************************
      driverClassName: oracle.jdbc.OracleDriver
      userName: rmsmonitor
      password: qwe123!@#

    datasource[4]:
      code: b32a5284483b4edc9291dd2b84c3b5f3
      name: 风控指标系统
      url: *****************************************
      driverClassName: oracle.jdbc.OracleDriver
      userName: fxsjzl
      password: fxsjzl123

    datasource[5]:
      code: b32a5284483b4edc1111dd2b84c3b5f3
      name: 风险管理平台
      url: *********************************************************************************************************************************
      driverClassName: com.mysql.cj.jdbc.Driver
      userName: rmpdba
      password: Fxglpt_dba@2022


    datasource[6]:
      code: b31l5284483b4edc1111dd2b84c3b5f3
      name: 风险集市
      url: *****************************************
      driverClassName: oracle.jdbc.OracleDriver
      userName: sys_crap
      password: Crap5585354

    datasource[7]:
      code: b33m5284483b4edc1111dd2b84c3b5f3
      name: 流动性风险
      url: *****************************************
      driverClassName: oracle.jdbc.OracleDriver
      userName: FXGLPT
      password: FXGLPT
    datasource[8]:
      code: b33m5284483b4edc1111dd2b84c3b5f4
      name: 融汇通金
      url: ******************************************
      driverClassName: oracle.jdbc.OracleDriver
      userName: fkxt
      password: fkxt
    datasource[9]:
      code: b33m5284483b4edc1111dd2b84c3b5f5
      name: 信用风险
      url: ****************************************
      driverClassName: oracle.jdbc.OracleDriver
      userName: sys_rmwp
      password: sys_Rmwp