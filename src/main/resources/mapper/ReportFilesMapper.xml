<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bstek.designer.mapper.ReportFilesMapper">

    <resultMap id="ReportFiles" type="com.bstek.designer.bean.PO.ReportFiles">
        <id property="id" column="id"/>
        <result property="createDate" column="create_date"/>
        <result property="reportName" column="report_name"/>
        <result property="fileName" column="file_name"/>
        <result property="updateDate" column="update_date"/>
        <result property="content" column="content"/>
        <result property="groupType" column="group_type"/>
    </resultMap>
    <select id="queryAllReports"  resultMap="ReportFiles"  parameterType="map" >
       select b.*  from (SELECT report_name, file_name, create_date, update_date, id, content,t3.group_name as group_type,publish_menu_name,menu_id,update_by
        FROM r_files a
        left join
        (select t1.report_id,GROUP_CONCAT(t2.group_name separator ',')  AS group_name  from r_file_type_mapping t1 left join r_group_info t2 on
            t1.group_id = t2.id
        group by t1.report_id )t3
        on a.id  = t3.report_id) b
        <where >
            <if test="reportName != null and reportName != '' ">
               and b.report_name like concat('%', #{reportName}, '%')
            </if>
            <if test="groupId != null and groupId != '' ">
                and b.group_type =  #{groupId}
            </if>
        </where>
        order by b.update_date desc
    </select>


    <select id="queryReportsByUserPermission" resultMap="ReportFiles" parameterType="map">
    select b.* from (
        SELECT report_name, file_name, create_date, update_date, id, content, t3.group_name as group_type, publish_menu_name, menu_id, update_by, create_by
        FROM r_files a
        left join
        (select t1.report_id, GROUP_CONCAT(t2.group_name separator ',') AS group_name from r_file_type_mapping t1 left join r_group_info t2 on
            t1.group_id = t2.id
        group by t1.report_id) t3
        on a.id = t3.report_id
        WHERE a.create_by = #{userId}
        
        UNION
        
        SELECT a.report_name, a.file_name, a.create_date, a.update_date, a.id, a.content, t3.group_name as group_type, a.publish_menu_name, a.menu_id, a.update_by, a.create_by
        FROM r_files a
        INNER JOIN r_report_permission p ON a.id = p.report_id
        left join
        (select t1.report_id, GROUP_CONCAT(t2.group_name separator ',') AS group_name from r_file_type_mapping t1 left join r_group_info t2 on
            t1.group_id = t2.id
        group by t1.report_id) t3
        on a.id = t3.report_id
        WHERE p.user_id = #{userId}
    ) b
    <where>
        <if test="reportName != null and reportName != ''">
            and b.report_name like concat('%', #{reportName}, '%')
        </if>
        <if test="groupId != null and groupId != ''">
            and b.group_type = #{groupId}
        </if>
    </where>
    order by b.update_date desc
</select>
</mapper>