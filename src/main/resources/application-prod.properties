
server.port=8084
#server.servlet.context-path=/ureport

#logging.level.root=info
#logging.file=log.log
logging.level.root=error

shiro.enabled=false
shiro.web.enabled=false

spring.http.encoding.force=true
spring.jackson.time-zone=Asia/Shanghai

spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB

#user login
ureport.username=admin
ureport.password=123456
#permissions expires - second
ureport.token.expires=86400
ureport.token.secret-key=6fa87140-3a53-11ed-ad8e-89304f903851
ureport.api.header.token.key=X-Token


# ??impala????????
principal=<EMAIL>
keytabPath=/home/<USER>/DCFXGL_GW001.keytab
keyConf=/etc/krb5.conf
dataUser=DCFXGL_GW001

# ?????

spring.datasource.dynamic.primary=master

spring.datasource.dynamic.datasource.master.url=************************************************************************************************************
spring.datasource.dynamic.datasource.master.username=rmpdba
spring.datasource.dynamic.datasource.master.password=Fxglpt_dba@2022
spring.datasource.dynamic.datasource.master.driver-class-name=com.mysql.cj.jdbc.Driver

spring.datasource.dynamic.datasource.slave.url=*******************************************
spring.datasource.dynamic.datasource.slave.username=etl
spring.datasource.dynamic.datasource.slave.password=etl5585354
spring.datasource.dynamic.datasource.slave.driver-class-name=oracle.jdbc.OracleDriver



#  ??????
urreport.filePath =/home/<USER>/guosenrmp/deploy/service/guoxinReport/file
# uat????????
saveMenuUrl=http://************:8903/base.api/RMIService
# admin??
platformUser=admin
platformPwd=0OBekc0tGJ/hLvwQPE9Z3w==


mybatis-plus.mapper-locations=classpath:/mapper/*Mapper.xml
mybatis-plus.type-aliases-package=com.bstek.designer.bean.PO
mybatis-plus.configuration.map-underscore-to-camel-case=true
mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl



spring.mail.default-encoding=utf-8
spring.mail.host=xcim005.web.fwxt.dgdmz
spring.mail.username=<EMAIL>
spring.mail.password=Rmp@sys#5585354
spring.mail.from=<EMAIL>
spring.mail.personal=é£é©ç®¡çå·¥ä½å¹³å°
spring.mail.properties.mail.smtp.auth=true
#spring.mail.properties.mail.smtp.auth.starttls.enable=true
#spring.mail.properties.mail.smtp.auth.starttls.required=true
spring.mail.properties.smtp.auth=true
spring.mail.properties.smtp.port=25
email.from=<EMAIL>
spring.mail.properties.mail.mime.charset=UTF-8
spring.mail.properties.mail.mime.splitlongparameters=false
exportURL=http://************:8084/ureport/download/excel
reportToken=gxzq
project.static=/home/<USER>/guosenrmp/deploy/service/guoxin/


logging.file.name=report
logging.file.path=./logs/
logging.level.root=info
logging.level.com.improve.fuqige.bronze=info
logging.pattern.console=%cyan(%d{yyyy-MM-dd HH:mm:ss.SSS}) %yellow([%thread]) %highlight(%-5level) %boldGreen(%logger{80}[LineNumber:%L]): %highlight(%msg%n)
logging.pattern.file=%d{yyyy-MM-dd HH:mm:ss.SSS} [%X{requestId}] %-5level --- [%thread] %logger{80}[LineNumber:%L]: %msg%n

# AWS S3 Configuration - Production Environment
aws.s3.bucket=
aws.s3.access-key=
aws.s3.secret-key=
aws.s3.endpoint=
aws.s3.region=us-east-1
aws.s3.path-style-access=true
