
server.port=8084
#server.servlet.context-path=/ureport

#logging.level.root=info
#logging.file=log.log

shiro.enabled=false
shiro.web.enabled=false

spring.http.encoding.force=true
spring.jackson.time-zone=Asia/Shanghai

spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB

#user login
ureport.username=admin
ureport.password=123456
#permissions expires - second
ureport.token.expires=0
ureport.token.secret-key=6fa87140-3a53-11ed-ad8e-89304f903851
ureport.api.header.token.key=X-Token


principal=<EMAIL>
keytabPath=D:/krb/tenant220718111804.keytab
keyConf=D:/krb/krb5.conf
dataUser=tenant220718111804

# ?????????
spring.datasource.dynamic.datasource.master.url=*********************************************************************************************************************************************************************************
spring.datasource.dynamic.datasource.master.username=rmptestdba
spring.datasource.dynamic.datasource.master.password=Fxglpttest_dba@2023
spring.datasource.dynamic.datasource.master.driver-class-name=com.mysql.jdbc.Driver

spring.datasource.dynamic.datasource.slave.url=********************************************
spring.datasource.dynamic.datasource.slave.username=etl
spring.datasource.dynamic.datasource.slave.password=etl5585354
spring.datasource.dynamic.datasource.slave.driver-class-name=oracle.jdbc.OracleDriver



#??ureport?????????·??
urreport.filePath = D://deploy//ureport
saveMenuUrl=http://*************/base.api/RMIService
platformUser=admin
platformPwd=T8lirTPfMgwYkoJk2EGEIA==

nohup java -jar -Dspring.profiles.active=local sunlineReport-core-2.3.0-SNAPSHOT.jar > /logs/guoxinReport.log 2>&1 &


mybatis-plus.mapper-locations=classpath:/mapper/*Mapper.xml
mybatis-plus.type-aliases-package=com.bstek.designer.bean.PO
mybatis-plus.configuration.map-underscore-to-camel-case=true
mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl

# AWS S3 Configuration
aws.s3.bucket=test
aws.s3.access-key=OCDXW7C5QDQQ7ETEL2Q6
aws.s3.secret-key=6fo3ADAwoNAXYshia2GebEuYNlyqAP1PRr6ueP4o
aws.s3.endpoint=http://***********:8000
aws.s3.region=us-east-1
aws.s3.path-style-access=true

spring.mail.default-encoding=utf-8
spring.mail.host=***********
spring.mail.username=<EMAIL>
spring.mail.password=1
spring.mail.from=<EMAIL>
spring.mail.personal=????????
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.auth.starttls.enable=true
spring.mail.properties.mail.smtp.auth.starttls.required=true
email.from=<EMAIL>
spring.mail.properties.mail.mime.charset=UTF-8
spring.mail.properties.mail.mime.splitlongparameters=false
exportURL=http://localhost:8084/ureport/download/excel
reportToken=gxzq
project.static=D:/deploy/service/guoxin/


logging.file.name=report
logging.file.path=./logs/
logging.level.root=info
logging.level.com.improve.fuqige.bronze=info
logging.pattern.console=%cyan(%d{yyyy-MM-dd HH:mm:ss.SSS}) %yellow([%thread]) %highlight(%-5level) %boldGreen(%logger{80}[LineNumber:%L]): %highlight(%msg%n)
logging.pattern.file=%d{yyyy-MM-dd HH:mm:ss.SSS} [%X{requestId}] %-5level --- [%thread] %logger{80}[LineNumber:%L]: %msg%n
